require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/miaosha/pay"],{"20e3":function(o,e,t){"use strict";(function(o,t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=getApp(),i={data:function(){return{opt:{},outmember:{},loading:!1,isload:!1,menuindex:-1,pre_url:a.globalData.pre_url,detailurl:"",tourl:"",typeid:"0",wxpay:0,wxpay_type:0,alipay:0,baidupay:0,toutiaopay:0,moneypay:0,cancod:0,daifu:0,daifu_txt:"好友代付",pay_month:0,pay_transfer:0,codtxt:"",pay_month_txt:"",give_coupon_list:[],give_coupon_num:0,userinfo:[],paypwd:"",hiddenmodalput:!0,payorder:{},tmplids:[],give_coupon_show:!1,give_coupon_close_url:"",more_alipay:0,alipay2:0,alipay3:0,paypal:0,payimg:"",yuanbao_money:0,total_yuanbao:0,yuanbao_msg:"",yuanbaopay:0,open_pay:!1,pay_type:"",invite_free:"",invite_status:!1,free_tmplids:"",sharepic:a.globalData.initdata.logo}},onShareAppMessage:function(){var o=this.sharepic,e=a.globalData.pre_url+"/h5/"+a.globalData.aid+".html#/pages/pay/daifu?scene=id_"+this.payorder.id,t=this._sharewx({title:"您有一份好友代付待查收，请尽快处理！",tolink:e,pic:o});return console.log("pay share data"),console.log(t),t},onShareTimeline:function(){var o=this.sharepic,e=a.globalData.pre_url+"/h5/"+a.globalData.aid+".html#/pages/pay/daifu?scene=id_"+this.payorder.id,t=this._sharewx({title:"您有一份好友代付待查收，请尽快处理1！",tolink:e,pic:o}),i=t.path.split("?")[1],n=a.globalData.pre_url+"/h5/"+a.globalData.aid+".html#"+t.path.split("?")[0];return{title:t.title,imageUrl:t.imageUrl,query:i,link:n}},onLoad:function(o){this.opt=a.getopts(o),console.log(this.opt),this.opt.tourl&&(this.tourl=decodeURIComponent(this.opt.tourl)),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var o=this;o.loading=!0;var e="";"mp"!=a.globalData.platform&&"h5"!=a.globalData.platform||(e=location.href),a.post("ApiMiaosha/pay",{orderid:o.opt.id,thisurl:e,tourl:o.tourl,scene:a.globalData.scene},(function(e){if(o.loading=!1,0!=e.status){if(o.wxpay=e.wxpay,o.outmember=e.outmember,o.wxpay_type=e.wxpay_type,o.alipay=e.alipay,o.baidupay=e.baidupay,o.toutiaopay=e.toutiaopay,o.cancod=e.cancod,o.codtxt=e.codtxt,o.daifu=e.daifu,o.daifu_txt=e.daifu_txt,o.pay_money=e.pay_money,o.pay_money_txt=e.pay_money_txt,o.moneypay=e.moneypay,o.pay_transfer=e.pay_transfer,o.pay_transfer_info=e.pay_transfer_info,o.pay_month=e.pay_month,o.pay_month_txt=e.pay_month_txt,o.payorder=e.payorder,o.userinfo=e.userinfo,o.tmplids=e.tmplids,o.give_coupon_list=e.give_coupon_list,o.give_coupon_list)for(var t in o.give_coupon_list)o.give_coupon_num+=o.give_coupon_list[t]["givenum"];o.detailurl=e.detailurl,o.tourl=e.tourl,o.paypal=e.paypal||0,o.more_alipay=e.more_alipay,o.alipay2=e.alipay2,o.alipay3=e.alipay3,o.yuanbao_money=e.yuanbao_money,o.total_yuanbao=e.total_yuanbao,o.yuanbao_msg=e.yuanbao_msg,o.yuanbaopay=e.yuanbaopay,o.wxpay?22==o.wxpay_type?o.typeid=22:o.typeid=2:o.moneypay?o.typeid=1:o.alipay?(o.typeid=3,2==o.alipay&&(o.typeid=23)):o.more_alipay?(o.alipay2&&(o.typeid=31),o.alipay3&&(o.typeid=32)):o.baidupay?o.typeid=11:o.toutiaopay&&(o.typeid=12),0==o.payorder.money&&o.payorder.score>0&&(o.typeid=1),e.invite_free&&(o.invite_free=e.invite_free),e.free_tmplids&&(o.free_tmplids=e.free_tmplids),o.loaded(),o.opt&&"success"==o.opt.paypal&&(o.typeid=51,a.showLoading("支付中"),a.post("ApiPay/paypalRedirect",{orderid:o.opt.id,paymentId:o.opt.paymentId,PayerID:o.opt.PayerID},(function(e){a.showLoading(!1),1==e.status?(a.success(e.msg),o.subscribeMessage((function(){o.invite_free?o.invite_status=!0:setTimeout((function(){o.give_coupon_list&&o.give_coupon_list.length>0?(o.give_coupon_show=!0,o.give_coupon_close_url=o.tourl):o.gotourl(o.tourl,"reLaunch")}),1e3)}))):0==e.status&&a.error(e.msg)})))}else a.error(e.msg)}))},getpwd:function(o,e){this.paypwd=e,this.topay({currentTarget:{dataset:{typeid:1}}})},changeradio:function(o){var e=o.currentTarget.dataset.typeid;this.typeid=e,console.log(e)},uploadpayimg:function(){var o=this;a.chooseImage((function(e){o.payimg=e[0]}),1)},topay:function(e){var i=this,n=i.typeid,s=this.payorder.id;if(1==n){if(i.userinfo.haspwd&&""==i.paypwd)return void i.$refs.dialogInput.open();a.confirm("确定已经支付吗?",(function(){a.showLoading("提交中"),a.post("ApiMiaosha/pay",{op:"submit",orderid:s,typeid:5,paypwd:i.paypwd,pay_type:i.pay_type},(function(o){if(a.showLoading(!1),a.error(o.msg),0!=o.status)return 2==o.status?(a.success(o.msg),void i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}))):void 0;a.error(o.msg)}))}))}else if(2==n)console.log(a),a.showLoading("提交中"),a.post("ApiPay/pay",{op:"submit",orderid:s,typeid:n},(function(e){if(a.showLoading(!1),0!=e.status){if(2==e.status)return a.success(e.msg),void i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}));var n=e.data;"wx"==a.globalData.platform?"shop"==i.payorder.type||2==i.wxpay_type?n.orderInfo?(console.log("requestOrderPayment1"),o.requestOrderPayment({timeStamp:n.timeStamp,nonceStr:n.nonceStr,package:n.package,signType:n.signType?n.signType:"MD5",paySign:n.paySign,orderInfo:n.orderInfo,success:function(o){a.success("付款完成"),i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}))},fail:function(o){}})):(console.log("requestOrderPayment2"),o.requestOrderPayment({timeStamp:n.timeStamp,nonceStr:n.nonceStr,package:n.package,signType:n.signType?n.signType:"MD5",paySign:n.paySign,success:function(o){a.success("付款完成"),i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}))},fail:function(o){}})):t.requestPayment({provider:"wxpay",timeStamp:n.timeStamp,nonceStr:n.nonceStr,package:n.package,signType:n.signType?n.signType:"MD5",paySign:n.paySign,success:function(o){a.success("付款完成"),i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}))},fail:function(o){}}):"mp"==a.globalData.platform||("h5"==a.globalData.platform?location.href=n.wx_url+"&redirect_url="+encodeURIComponent(location.href.split("#")[0]+"#"+i.tourl):"app"==a.globalData.platform?(console.log(n),t.requestPayment({provider:"wxpay",orderInfo:n,success:function(o){a.success("付款完成"),i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}))},fail:function(o){console.log(o)}})):"qq"==a.globalData.platform&&qq.requestWxPayment({url:n.wx_url,referer:n.referer,success:function(o){i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}))},fail:function(o){}}))}else a.error(e.msg)}));else if(3==n||31==n||32==n)a.showLoading("提交中"),a.post("ApiPay/pay",{op:"submit",orderid:s,typeid:n},(function(o){if(console.log(o),a.showLoading(!1),0!=o.status){if(2==o.status)return a.success(o.msg),void i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}));var e=o.data;"alipay"==a.globalData.platform?t.requestPayment({provider:"alipay",orderInfo:e.trade_no,success:function(o){console.log(o),"6001"!=o.resultCode&&(a.success("付款完成"),i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)})))},fail:function(o){}}):"mp"==a.globalData.platform||"h5"==a.globalData.platform?(document.body.innerHTML=o.data,document.forms["alipaysubmit"].submit()):"app"==a.globalData.platform&&(console.log("------------alipay----------"),console.log(e),console.log("------------alipay end----------"),t.requestPayment({provider:"alipay",orderInfo:e,success:function(o){console.log("------------success----------"),console.log(o),a.success("付款完成"),i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}))},fail:function(o){console.log(o)}}))}else a.error(o.msg)}));else if("11"==n)a.showLoading("提交中"),a.post("ApiPay/pay",{op:"submit",orderid:s,typeid:n},(function(o){a.showLoading(!1),swan.requestPolymerPayment({orderInfo:o.orderInfo,success:function(o){a.success("付款完成"),i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}))},fail:function(o){2!=o.errCode&&a.alert(JSON.stringify(o))}})}));else if("12"==n)a.showLoading("提交中"),a.post("ApiPay/pay",{op:"submit",orderid:s,typeid:n},(function(o){a.showLoading(!1),console.log(o.orderInfo),tt.pay({service:5,orderInfo:o.orderInfo,success:function(o){0===o.code&&(a.success("付款完成"),i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)})))},fail:function(o){a.alert(JSON.stringify(o))}})}));else if("22"==n)if("wx"==a.globalData.platform)o.login({success:function(e){e.code?(a.showLoading("提交中"),a.post("ApiPay/getYunMpauthParams",{jscode:e.code},(function(e){a.showLoading(!1),a.post("https://showmoney.cn/scanpay/fixed/mpauth",e.params,(function(e){console.log(e.sessionKey),a.post("ApiPay/getYunUnifiedParams",{orderid:s,sessionKey:e.sessionKey},(function(e){a.post("https://showmoney.cn/scanpay/unified",e.params,(function(e){"09"==e.respcd?o.requestPayment({timeStamp:e.timeStamp,nonceStr:e.nonceStr,package:e.package,signType:e.mpSignType,paySign:e.mpSign,success:function(o){a.success("付款完成"),i.subscribeMessage((function(){i.invite_free?i.invite_status=!0:setTimeout((function(){i.give_coupon_list&&i.give_coupon_list.length>0?(i.give_coupon_show=!0,i.give_coupon_close_url=i.tourl):i.gotourl(i.tourl,"reLaunch")}),1e3)}))},fail:function(o){}}):a.alert(e.errorDetail)}))}))}))}))):console.log("登录失败！"+e.errMsg)}});else{var u=a.globalData.baseurl+"ApiPay/pay&aid="+a.globalData.aid+"&platform="+a.globalData.platform+"&session_id="+a.globalData.session_id;u+="&op=submit&orderid="+s+"&typeid=22",location.href=u}else{if("23"==n)return setTimeout((function(){i.$refs.dialogPayconfirm.open()}),1e3),void a.goto("/pages/index/webView2?orderid="+s+"&typeid=23&aid="+a.globalData.aid+"&platform="+a.globalData.platform+"&session_id="+a.globalData.session_id);if("24"==n)return void a.goto("/pages/index/webView2?orderid="+s+"&typeid=24");if("yuanbao"==n){console.log(a);var r=i.total_yuanbao-0,p=i.userinfo.yuanbao-0;if(r>p)return void a.alert(i.t("元宝")+"不足");i.open_pay=!0,i.pay_type="yuanbao"}else"51"==n&&(a.showLoading("提交中"),a.post("ApiPay/pay",{op:"submit",orderid:s,typeid:n},(function(o){if(a.showLoading(!1),console.log(o),1==o.status)if("app"==a.globalData.platform){var e=plus.webview.create("","custom-webview",{top:t.getSystemInfoSync().statusBarHeight+44});e.loadURL(o.data);var n=i.$scope.$getAppWebview();n.append(e)}else a.goto("url::"+o.data);else a.alert(o.msg)})))}},topay2:function(){var o=this,e=this.payorder.id;a.confirm("确定要"+o.codtxt+"吗?",(function(){a.showLoading("提交中"),a.post("ApiPay/pay",{op:"submit",orderid:e,typeid:4},(function(e){if(a.showLoading(!1),0!=e.status)return 2==e.status?(a.success(e.msg),void o.subscribeMessage((function(){setTimeout((function(){o.gotourl(o.tourl,"reLaunch")}),1e3)}))):void 0;a.error(e.msg)}))}))},topayMonth:function(){var o=this,e=this.payorder.id;a.confirm("确定要"+o.pay_month_txt+"支付吗?",(function(){a.showLoading("提交中"),a.post("ApiPay/pay",{op:"submit",orderid:e,typeid:41},(function(e){if(a.showLoading(!1),0!=e.status)return 2==e.status?(a.success(e.msg),void o.subscribeMessage((function(){setTimeout((function(){o.gotourl(o.tourl,"reLaunch")}),1e3)}))):void 0;a.error(e.msg)}))}))},topayTransfer:function(o){var e=this.payorder.id;a.goto("/pagesExa/miaosha/payshangchuan?id="+e)},give_coupon_close:function(o){var e=o.currentTarget.dataset.url;this.give_coupon_show=!1,this.gotourl(e,"reLaunch")},gotourl:function(o,e){if(("mp"==a.globalData.platform||"h5"==a.globalData.platform)&&0===o.indexOf("miniProgram::")){o=o.slice(13);var t=o.split("|");return console.log(t),void this.showOpenWeapp()}a.goto(o,e)},showOpenWeapp:function(){this.$refs.dialogOpenWeapp.open()},closeOpenWeapp:function(){this.$refs.dialogOpenWeapp.close()},PayconfirmFun:function(){this.gotourl(this.tourl,"reLaunch")},close_pay:function(){this.open_pay=!1},closeInvite:function(){var o=this;o.invite_status=!1,setTimeout((function(){o.give_coupon_list&&o.give_coupon_list.length>0?(o.give_coupon_show=!0,o.give_coupon_close_url=o.tourl):o.gotourl(o.tourl,"reLaunch")}),1e3)},copybankcarduser:function(e){var t=e.currentTarget.dataset.bankcarduser;o.setClipboardData({data:t,success:function(e){o.showToast({title:"复制成功"})}})},copybankcardnum:function(e){var t=e.currentTarget.dataset.bankcardnum;o.setClipboardData({data:t,success:function(e){o.showToast({title:"复制成功"})}})},gotoInvite:function(){var o=this.free_tmplids;o&&o.length>0&&t.requestSubscribeMessage({tmplIds:o,success:function(o){console.log(o)},fail:function(o){console.log(o)}}),a.goto("/pagesExt/invite_free/index","reLaunch")},todaifu:function(o){var e=this,i=a.getplatform();e.payorder.id;if("mp"==i||"h5"==i){var n=a.globalData.pre_url+"/h5/"+a.globalData.aid+".html#/pages/pay/daifu?scene=id_"+e.payorder.id;this._sharemp({title:"您有一份好友代付待查收，请尽快处理~",link:n,pic:e.sharepic}),a.error("点击右上角发送给好友或分享到朋友圈")}else"app"==i?t.showActionSheet({itemList:["发送给微信好友","分享到微信朋友圈"],success:function(o){if(o.tapIndex>=0){var i="WXSceneSession";1==o.tapIndex&&(i="WXSenceTimeline");var n={provider:"weixin",type:0};n.scene=i,n.title="您的好友向您发出了代付请求",n.summary="您有一份好友代付待查收，请尽快处理~",n.href=a.globalData.pre_url+"/h5/"+a.globalData.aid+".html#/pages/pay/daifu?scene=id_"+e.payorder.id,n.imageUrl="",t.share(n)}}}):a.error("该终端不支持此操作")}}};e.default=i}).call(this,t("3223")["default"],t("df3c")["default"])},2602:function(o,e,t){"use strict";t.d(e,"b",(function(){return i})),t.d(e,"c",(function(){return n})),t.d(e,"a",(function(){return a}));var a={uniPopup:function(){return Promise.all([t.e("common/vendor"),t.e("components/uni-popup/uni-popup")]).then(t.bind(null,"ca44a"))},uniPopupDialog:function(){return t.e("components/uni-popup-dialog/uni-popup-dialog").then(t.bind(null,"267c"))},loading:function(){return t.e("components/loading/loading").then(t.bind(null,"ceaa"))},dpTabbar:function(){return t.e("components/dp-tabbar/dp-tabbar").then(t.bind(null,"b875"))},popmsg:function(){return t.e("components/popmsg/popmsg").then(t.bind(null,"2bf2"))}},i=function(){var o=this,e=o.$createElement,t=(o._self._c,o.isload&&0!=o.payorder.score&&o.payorder.money>0&&o.payorder.score>0?o.t("积分"):null),a=!o.isload||0==o.payorder.score||o.payorder.money>0&&o.payorder.score>0?null:o.t("积分"),i=o.isload&&"0"!=o.typeid?o.t("color1"):null,n=o.isload&&1==o.pay_transfer?o.t("color2"):null,s=o.isload&&1==o.pay_transfer?o.t("转账汇款"):null,u=o.isload&&1==o.pay_transfer?o.t("转账汇款"):null,r=o.isload&&1==o.pay_month?o.t("color1"):null,p=o.isload&&o.daifu?"h5"==o.getplatform()||"mp"==o.getplatform()||"app"==o.getplatform():null,l=o.isload&&o.give_coupon_show?o.t("优惠券"):null,c=o.isload&&1==o.yuanbaopay&&o.open_pay&&1==o.wxpay&&(0==o.wxpay_type||1==o.wxpay_type||2==o.wxpay_type||3==o.wxpay_type)&&"2"==o.typeid?o.t("color1"):null,d=o.isload&&1==o.yuanbaopay&&o.open_pay&&1==o.wxpay&&22==o.wxpay_type&&"22"==o.typeid?o.t("color1"):null,y=o.isload&&1==o.yuanbaopay&&o.open_pay&&2==o.alipay&&"23"==o.typeid?o.t("color1"):null,g=o.isload&&1==o.yuanbaopay&&o.open_pay&&1==o.alipay&&"3"==o.typeid?o.t("color1"):null,_=o.isload&&1==o.yuanbaopay&&o.open_pay&&1==o.more_alipay&&1==o.alipay2&&"31"==o.typeid?o.t("color1"):null,f=o.isload&&1==o.yuanbaopay&&o.open_pay&&1==o.more_alipay&&1==o.alipay3&&"32"==o.typeid?o.t("color1"):null,m=o.isload&&1==o.yuanbaopay&&o.open_pay&&1==o.baidupay&&"11"==o.typeid?o.t("color1"):null,h=o.isload&&1==o.yuanbaopay&&o.open_pay&&1==o.toutiaopay&&"12"==o.typeid?o.t("color1"):null,v=o.isload&&1==o.yuanbaopay&&o.open_pay&&1==o.moneypay?o.t("余额"):null,b=o.isload&&1==o.yuanbaopay&&o.open_pay&&1==o.moneypay&&"1"==o.typeid?o.t("color1"):null,w=o.isload&&1==o.yuanbaopay&&o.open_pay&&"0"!=o.typeid?o.t("color1"):null;o.$mp.data=Object.assign({},{$root:{m0:t,m1:a,m2:i,m3:n,m4:s,m5:u,m6:r,m7:p,m8:l,m9:c,m10:d,m11:y,m12:g,m13:_,m14:f,m15:m,m16:h,m17:v,m18:b,m19:w}})},n=[]},"2f624":function(o,e,t){"use strict";t.r(e);var a=t("2602"),i=t("8197");for(var n in i)["default"].indexOf(n)<0&&function(o){t.d(e,o,(function(){return i[o]}))}(n);t("4096");var s=t("828b"),u=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=u.exports},4096:function(o,e,t){"use strict";var a=t("bc5e"),i=t.n(a);i.a},8197:function(o,e,t){"use strict";t.r(e);var a=t("20e3"),i=t.n(a);for(var n in a)["default"].indexOf(n)<0&&function(o){t.d(e,o,(function(){return a[o]}))}(n);e["default"]=i.a},bc5e:function(o,e,t){},bd605:function(o,e,t){"use strict";(function(o,e){var a=t("47a9");t("06e9");a(t("3240"));var i=a(t("2f624"));o.__webpack_require_UNI_MP_PLUGIN__=t,e(i.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])}},[["bd605","common/runtime","common/vendor"]]]);