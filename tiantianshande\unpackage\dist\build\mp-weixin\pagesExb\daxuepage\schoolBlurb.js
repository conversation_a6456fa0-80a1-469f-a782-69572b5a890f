require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExb/daxuepage/schoolBlurb"],{2255:function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var u=function(){var t=this.$createElement;this._self._c},a=[]},"248f5":function(t,n,e){"use strict";(function(t,n){var u=e("47a9");e("06e9");u(e("3240"));var a=u(e("c9da"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},a673:function(t,n,e){"use strict";e.r(n);var u=e("ceb8"),a=e.n(u);for(var o in u)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(o);n["default"]=a.a},c9da:function(t,n,e){"use strict";e.r(n);var u=e("2255"),a=e("a673");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);var c=e("828b"),i=Object(c["a"])(a["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);n["default"]=i.exports},ceb8:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=getApp(),a={data:function(){return{opt:{},content:""}},onLoad:function(t){this.opt=u.getopts(t);var n=this;u.get("ApiDaxue/index",{id:this.opt.id},(function(t){n.content=t.daxue.content}))},methods:{}};n.default=a}},[["248f5","common/runtime","common/vendor"]]]);