require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/my/setidcardimg"],{"4c3b7":function(n,t,e){},"5cf7":function(n,t,e){"use strict";(function(n,t){var a=e("47a9");e("06e9");a(e("3240"));var i=a(e("9188"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"7abda":function(n,t,e){"use strict";e.r(t);var a=e("90e0"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(o);t["default"]=i.a},"90e0":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,realname:"",idcard:"",textset:{},haspwd:0,idcard_front:"../../static/img/upload.png",idcard_back:"../../static/img/upload.png"}},onLoad:function(n){this.loaded();var t=this;a.get("ApiMy/set",{},(function(n){console.log(n);var e=n.userinfo;t.realname=e.realname,t.idcard=e.usercard||"",e.is_eid_verify&&a.goto("/pages/my/seteid")}))},onPullDownRefresh:function(){},methods:{upIdcardHead:function(){var n=this;a.chooseImage((function(t){n.idcard_front=t[0]}))},upIdcardBack:function(){var n=this;a.chooseImage((function(t){n.idcard_back=t[0]}))},formSubmit:function(){var n=this.realname,t=this.idcard,e=this.idcard_front,i=this.idcard_back;this.is_eid_verify;""!=n?""!=t?"../../static/img/upload.png"!=e?"../../static/img/upload.png"!=i?(a.showLoading("提交中"),a.post("ApiMy/setfield2",{realname:n,idcard:t,idcard_back:i,idcard_front:e},(function(n){a.showLoading(!1),1==n.status?(a.success(n.msg),setTimeout((function(){a.goto("/pages/my/seteid")}),1e3)):a.error(n.msg)}))):a.alert("请上传身份证背面"):a.alert("请上传身份证正面"):a.alert("请输入身份证号码"):a.alert("请输入姓名")}}};t.default=i},9188:function(n,t,e){"use strict";e.r(t);var a=e("dd9d"),i=e("7abda");for(var o in i)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(o);e("9c6b");var r=e("828b"),d=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"5fe237e2",null,!1,a["a"],void 0);t["default"]=d.exports},"9c6b":function(n,t,e){"use strict";var a=e("4c3b7"),i=e.n(a);i.a},dd9d:function(n,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){return a}));var a={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},i=function(){var n=this.$createElement;this._self._c},o=[]}},[["5cf7","common/runtime","common/vendor"]]]);