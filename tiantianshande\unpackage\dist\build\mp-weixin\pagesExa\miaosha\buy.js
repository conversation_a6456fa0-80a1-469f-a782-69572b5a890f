require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/miaosha/buy"],{"6d2f":function(e,t,i){"use strict";i.r(t);var a=i("7dac"),o=i("eac7");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("c110");var s=i("828b"),n=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=n.exports},"7dac":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={loading:function(){return i.e("components/loading/loading").then(i.bind(null,"ceaa"))},dpTabbar:function(){return i.e("components/dp-tabbar/dp-tabbar").then(i.bind(null,"b875"))},popmsg:function(){return i.e("components/popmsg/popmsg").then(i.bind(null,"2bf2"))}},o=function(){var e=this,t=e.$createElement,i=(e._self._c,e.isload?e.__map(e.allbuydata,(function(t,i){var a=e.__get_orig(t),o=e.__map(t.prodata,(function(t,i){var a=e.__get_orig(t),o=t.product.glassrecord?e.t("color1rgb"):null;return{$orig:a,m0:o}})),r=1==t.freightList[t.freightkey].minpriceset&&t.freightList[t.freightkey].minprice>0&&1*t.freightList[t.freightkey].minprice>1*t.product_price?(t.freightList[t.freightkey].minprice-t.product_price).toFixed(2):null,s=1==t.freightList[t.freightkey].pstype?e.__map(t.freightList[t.freightkey].storedata,(function(i,a){var o=e.__get_orig(i),r=(a<5||1==e.storeshowall)&&t.freightList[t.freightkey].storekey==a?e.t("color1"):null;return{$orig:o,m1:r}})):null,n=1==t.freightList[t.freightkey].pstype?0==e.storeshowall&&t.freightList[t.freightkey].storedata.length>5:null,l=5==t.freightList[t.freightkey].pstype?e.__map(t.freightList[t.freightkey].storedata,(function(i,a){var o=e.__get_orig(i),r=(a<5||1==e.storeshowall)&&t.freightList[t.freightkey].storekey==a?e.t("color1"):null;return{$orig:o,m2:r}})):null,c=5==t.freightList[t.freightkey].pstype?0==e.storeshowall&&t.freightList[t.freightkey].storedata.length>5:null;return{$orig:a,l0:o,g0:r,l1:s,g1:n,l2:l,g2:c}})):null),a=e.isload?e.t("color1"):null,o=e.isload?e.t("color1rgb"):null;e.$mp.data=Object.assign({},{$root:{l3:i,m3:a,m4:o}})},r=[]},8046:function(e,t,i){"use strict";(function(e,t){var a=i("47a9");i("06e9");a(i("3240"));var o=a(i("6d2f"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},c110:function(e,t,i){"use strict";var a=i("c8f8"),o=i.n(a);o.a},c8f8:function(e,t,i){},eac7:function(e,t,i){"use strict";i.r(t);var a=i("f453"),o=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},f453:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=getApp(),a={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:i.globalData.pre_url,test:"test",havetongcheng:0,address:[],memberList:[],checkMem:{},usescore:0,scoredk_money:0,totalprice:"0.00",couponvisible:!1,cuxiaovisible:!1,membervisible:!1,memberinfovisible:!1,selectmemberinfo:{},bid:0,nowbid:0,needaddress:1,linkman:"",tel:"",userinfo:{},miaosha:{},outmember:{},pstimeDialogShow:!1,pstimeIndex:-1,manjian_money:0,cxid:0,cxids:[],latitude:"",longitude:"",allbuydata:{},allbuydatawww:{},alltotalprice:"",cuxiaoinfo:!1,cuxiaoList:{},type11visible:!1,type11key:-1,regiondata:"",items:[],editorFormdata:[],buy_selectmember:!1,multi_promotion:0,storeshowall:!1,order_change_price:!1,invoiceShow:!1,invoice:{},invoice_type:[],invoice_type_select:1,name_type_select:1,name_type_personal_disabled:!1,inputDisabled:!1,submitDisabled:!1,pstype3needAddress:!1,isshowglass:!1,glassrecordlist:[],grid:0,curindex:-1,curindex2:-1,ccid:0}},onLoad:function(e){this.opt=i.getopts(e),this.ccid=this.opt.ccid?this.opt.ccid:1,this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,i.get("ApiMiaosha/buy",{prodata:this.opt.id},(function(a){return t.loading=!1,"denglu"==a.status?(i.alert(a.msg),void setTimeout((function(){i.goto("/pagesB/login/login")}),1500)):"kahao"==a.status?(i.alert(a.msg),void setTimeout((function(){i.goto("/pagesExa/my/setshoukuan")}),1500)):"address"==a.status?(i.alert(a.msg),void setTimeout((function(){i.goto("/pages/address/addressadd")}),1500)):void(0!=a.status?(t.havetongcheng=a.havetongcheng,t.address=a.address,t.linkman=a.linkman,t.tel=a.tel,t.userinfo=a.userinfo,t.miaosha=a.miaosha,t.outmember=a.outmember,t.buy_selectmember=a.buy_selectmember,t.order_change_price=a.order_change_price,t.pstype3needAddress=a.pstype3needAddress,t.buy_selectmember&&e.request({url:i.globalData.pre_url+"/static/area2.json",data:{},method:"GET",header:{"content-type":"application/json"},success:function(e){t.items=e.data}}),t.allbuydata=a.allbuydata,t.allbuydatawww=JSON.parse(JSON.stringify(a.allbuydata)),console.log(t.allbuydata),t.needLocation=a.needLocation,t.scorebdkyf=a.scorebdkyf,t.multi_promotion=a.multi_promotion,t.calculatePrice(),t.loaded(),1==a.needLocation&&i.getLocation((function(e){var i=e.latitude,a=e.longitude;t.latitude=i,t.longitude=a;var o=t.allbuydata;for(var r in o){var s=o[r].freightList;for(var n in s)if(1==s[n].pstype||5==s[n].pstype){var l=s[n].storedata;if(l){for(var c in l)if(i&&a&&l[c].latitude&&l[c].longitude){var d=t.getDistance(i,a,l[c].latitude,l[c].longitude);l[c].juli=d}for(var c in l.sort((function(e,t){return e["juli"]-t["juli"]})),l)l[c].juli&&(l[c].juli=l[c].juli+"千米");console.log(l),o[r].freightList[n].storedata=l}}}t.allbuydata=o}))):a.msg?i.alert(a.msg,(function(){a.url?i.goto(a.url):i.goback()})):a.url?i.goto(a.url):i.alert("您没有权限购买该商品"))}))},scoredk:function(e){var t=e.detail.value[0];t||(t=0),this.usescore=t,this.calculatePrice()},inputLinkman:function(e){this.linkman=e.detail.value},inputTel:function(e){this.tel=e.detail.value},inputfield:function(e){var t=e.currentTarget.dataset.bid,i=e.currentTarget.dataset.field;allbuydata2[t][i]=e.detail.value,this.allbuydata2=allbuydata2},chooseAddress:function(){i.goto("/pages/address/address?fromPage=buy&type="+(1==this.havetongcheng?"1":"0"))},inputPrice:function(e){var t=e.currentTarget.dataset.index,a=e.currentTarget.dataset.index2,o=this.allbuydata,r=this.allbuydatawww,s=r[t]["prodata"][a].guige.sell_price;if(""==e.detail.value||parseFloat(e.detail.value)<parseFloat(s))return this.submitDisabled=!0,void i.error("不能小于原价:"+s);this.submitDisabled=!1,o[t]["prodata"][a].guige.sell_price=e.detail.value,o[t]["product_price"]=(e.detail.value*o[t]["prodata"][a].num).toFixed(2),this.allbuydata=o,console.log(o[t]),this.calculatePrice()},calculatePrice:function(){this.address;var e=this.allbuydata,t=0,i=0,a=0;for(var o in e){var r=parseFloat(e[o].product_price),s=parseFloat(e[o].leveldk_money),n=parseFloat(e[o].manjian_money),l=parseFloat(e[o].coupon_money),c=parseFloat(e[o].cuxiao_money),d=parseFloat(e[o].invoice_money),u=e[o].freightList[e[o].freightkey],h=u["freight_price"];1!=u.pstype&&3!=u.pstype&&4!=u.pstype&&(a=1),!this.pstype3needAddress||3!=u.pstype&&4!=u.pstype&&5!=u.pstype||(a=1),4==e[o].coupontype&&(h=0);var g=r-s-n-l+c;if(g<0&&(g=0),g+=h,e[o].freight_price=h.toFixed(2),e[o].business.invoice&&e[o].business.invoice_rate>0&&e[o].tempInvoice){d=g*parseFloat(e[o].business.invoice_rate)/100;e[o].invoice_money=d.toFixed(2),g+=d}console.log("invoice_money"),console.log(d),e[o].totalprice=g.toFixed(2),t+=g,i+=h}if(this.needaddress=a,this.usescore)var p=parseFloat(this.userinfo.scoredk_money);else p=0;var f=t;t-=p,t<0&&(t=0),"1"==this.scorebdkyf&&p>0&&t<i&&(t=i,p=f-i);var m=parseFloat(this.userinfo.scoredkmaxpercent),y=parseInt(this.userinfo.scoremaxtype),b=parseFloat(this.userinfo.scoredkmaxmoney);0==y&&p>0&&m>0&&m<100&&p>f*m*.01?(p=f*m*.01,t=f-p):1==y&&p>b&&(p=b,t=f-p),t<0&&(t=0),t=t.toFixed(2),this.alltotalprice=t,this.allbuydata=e},changeFreight:function(e){var t=this.allbuydata,a=e.currentTarget.dataset.bid,o=e.currentTarget.dataset.index,r=t[a].freightList;1==r[o].pstype&&r[o].storedata.length<1?i.error("无可自提门店"):5==r[o].pstype&&r[o].storedata.length<1?i.error("无可配送门店"):(t[a].freightkey=o,this.allbuydata=t,this.calculatePrice(),this.allbuydata[a].editorFormdata=[])},chooseFreight:function(t){for(var i=this,a=i.allbuydata,o=t.currentTarget.dataset.bid,r=a[o].freightList,s=[],n=0;n<r.length;n++)s.push(r[n].name);e.showActionSheet({itemList:s,success:function(e){e.tapIndex>=0&&(a[o].freightkey=e.tapIndex,i.allbuydata=a,i.calculatePrice())}})},choosePstime:function(e){for(var t=this.allbuydata,a=e.currentTarget.dataset.bid,o=t[a].freightkey,r=t[a].freightList,s=(r[o],r[o].pstimeArr),n=[],l=0;l<s.length;l++)n.push(s[l].title);0!=n.length?(this.nowbid=a,this.pstimeDialogShow=!0,this.pstimeIndex=-1):i.alert("当前没有可选"+(1==r[o].pstype?"取货":"配送")+"时间段")},pstimeRadioChange:function(e){var t=this.allbuydata,i=e.currentTarget.dataset.index,a=this.nowbid,o=t[a].freightkey,r=t[a].freightList,s=(r[o],r[o].pstimeArr),n=s[i];t[a].pstimetext=n.title,t[a].freight_time=n.value,this.allbuydata=t,this.pstimeDialogShow=!1},hidePstimeDialog:function(){this.pstimeDialogShow=!1},chooseCoupon:function(e){var t=this.allbuydata,a=e.bid,o=e.rid,r=e.key,s=t[a].coupons,n=t[a].couponrids,l=t[a].couponList;if(i.inArray(o,n)){var c=[],d=[];for(var u in s)s[u].id!=o&&(c.push(s[u]),d.push(s[u].id))}else if(c=s,d=n,console.log(t[a].coupon_peruselimit+"---"+n.length),t[a].coupon_peruselimit>n.length)console.log("xxxx"),c.push(l[r]),d.push(l[r].id);else{if(t[a].coupon_peruselimit>1)return void i.error("最多只能选用"+t[a].coupon_peruselimit+"张");c=[l[r]],d=[o]}console.log(c),console.log(d),t[a].coupons=c,t[a].couponrids=d;var h=0,g=1;for(var u in c)4==c[u]["type"]?g=4:10==c[u]["type"]?h+=c[u]["thistotalprice"]*(100-c[u]["discount"])*.01:h+=c[u]["money"];t[a].coupontype=g,t[a].coupon_money=h,this.allbuydata=t,this.couponvisible=!1,this.calculatePrice()},choosestore:function(e){var t=e.currentTarget.dataset.bid,i=e.currentTarget.dataset.index,a=this.allbuydata,o=a[t],r=o.freightkey;a[t].freightList[r].storekey=i,this.allbuydata=a},topay:function(e){var t=this,a=t.needaddress,o=this.address.id,r=this.checkMem.id,s=this.linkman,n=this.tel,l=this.usescore,c=t.opt.frompage?t.opt.frompage:"",d=t.allbuydata;if(0==a&&(o=0),1!=a||void 0!=o){var u=[];for(var h in d){var g=d[h].freightkey;if(1==d[h].freightList[g].pstimeset&&""==d[h].freight_time)return void i.error("请选择"+(1==d[h].freightList[g].pstype?"取货":"配送")+"时间");if(1==d[h].freightList[g].pstype||5==d[h].freightList[g].pstype)var p=d[h].freightList[g].storekey,f=d[h].freightList[g].storedata[p].id;else f=0;if(11==d[h].freightList[g].pstype){var m=d[h].type11key;if(0==m||!m)return void i.error("请选择物流");m-=1}else m=0;for(var y=d[h].freightList[g].formdata,b=e.detail.value,v={},x=0;x<y.length;x++){var _="form"+d[h].bid+"_"+x;if(1==y[x].val3&&(""===b[_]||void 0===b[_]||0==b[_].length))return void i.alert(y[x].val1+" 必填");if("selector"==y[x].key&&(b[_]=y[x].val2[b[_]]),x>0&&"确认账号"==y[x].val1&&"充值账号"==y[x-1].val1&&b[_]!=b["form"+d[h].bid+"_"+(x-1)])return void i.alert("两次输入账号不一致");v["form"+x]=b[_]}var L=d[h].couponrids.join(","),k={bid:d[h].bid,prodata:d[h].prodatastr,cuxiaoid:d[h].cuxiaoid,couponrid:L,freight_id:d[h].freightList[g].id,freight_time:d[h].freight_time,storeid:f,formdata:v,type11key:m};t.order_change_price&&(k.prodataList=d[h].prodata),d[h].business.invoice&&(k.invoice=d[h].tempInvoice),u.push(k)}i.showLoading("提交中"),i.post("ApiMiaosha/createOrder",{frompage:c,buydata:u,addressid:o,linkman:s,tel:n,miaoshaid:this.opt.id,checkmemid:r,usescore:l},(function(e){if(i.showLoading(!1),0!=e.status)return 6==e.status?(i.error(e.msg+",即将自动跳转"),void setTimeout((function(){i.goto("/pagesExa/my/setshoukuan?id="+t.opt.id)}),1500)):void(e.payorderid&&(i.alert("抢购成功"),setTimeout((function(){i.goto("/pagesExa/miaosha/classify2?ccid="+t.ccid)}),1500)));i.error(e.msg)}))}else i.error("请选择收货地址")},showCouponList:function(e){this.couponvisible=!0,this.bid=e.currentTarget.dataset.bid},showInvoice:function(e){this.invoiceShow=!0,this.bid=e.currentTarget.dataset.bid;var t=e.currentTarget.dataset.index;this.invoice_type=this.allbuydata[t].business.invoice_type,this.invoice=this.allbuydata[t].tempInvoice},changeOrderType:function(e){var t=e.detail.value;2==t?(this.name_type_select=2,this.name_type_personal_disabled=!0):this.name_type_personal_disabled=!1,this.invoice_type_select=t},changeNameType:function(e){var t=e.detail.value;this.name_type_select=t},invoiceFormSubmit:function(e){var t=e.detail.value;if(""!=t.invoice_name)if(2!=t.name_type&&2!=t.invoice_type||""!=t.tax_no){if(2==t.invoice_type){if(""==t.address)return void i.error("请填写注册地址");if(""==t.tel)return void i.error("请填写注册电话");if(""==t.bank_name)return void i.error("请填写开户银行");if(""==t.bank_account)return void i.error("请填写银行账号")}if(""==t.mobile||/^1[3456789]\d{9}$/.test(t.mobile))if(""==t.email||/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/.test(t.email))if(""!=t.mobile||""!=t.email){var a=this.allbuydata;for(var o in a)a[o].bid==this.bid&&(a[o].tempInvoice=t);this.allbuydata=a,this.invoiceShow=!1,this.calculatePrice()}else i.error("手机号和邮箱请填写其中一个");else i.error("邮箱有误，请重填");else i.error("手机号码有误，请重填")}else i.error("请填写公司税号");else i.error("请填写抬头名称")},handleClickMask:function(){this.couponvisible=!1,this.cuxiaovisible=!1,this.type11visible=!1,this.membervisible=!1,this.invoiceShow=!1},showCuxiaoList:function(e){this.cuxiaovisible=!0,this.bid=e.currentTarget.dataset.bid},changecx:function(e){var t=this,a=e.currentTarget.dataset.id,o=e.currentTarget.dataset.index;if(console.log(a),t.cxid=a,0!=a){var r=t.allbuydata[t.bid].cuxiaolist[o];console.log(r.cuxiaomoney),i.post("ApiShop/getcuxiaoinfo",{id:a},(function(e){4!=r.type&&5!=r.type||(e.cuxiaomoney=r.cuxiaomoney),t.cuxiaoinfo=e}))}else t.cuxiaoinfo=!1},changecxMulti:function(e){var t=this,a=e.currentTarget.dataset.id,o=e.currentTarget.dataset.index;if(t.cuxiaoList.length=0,console.log("cxid:"+a),0==a)return t.cuxiaoinfo=!1,t.cxids.length=0,void(t.cxid=0);var r=t.cxids.indexOf(a);if(-1===r?t.cxids.push(a):t.cxids.splice(r),0==t.cxids.length)return t.cxid=0,void(t.cuxiaoinfo=!1);t.cxid="";var s=t.allbuydata[t.bid].cuxiaolist[o];console.log(s.cuxiaomoney),i.showLoading(),i.post("ApiShop/getcuxiaoinfo",{id:t.cxids},(function(e){i.showLoading(!1),t.cuxiaoList=e}))},chooseCuxiao:function(){var e=this.allbuydata,t=this.bid,i=this.cxid;this.cxids;if(console.log(0==i),0==i||""==i)e[t].cuxiaoid="",e[t].cuxiao_money=0,e[t].cuxiaoname="不使用促销",e[t].cuxiaonameArr=[];else if(e[t].cuxiaoid=[],e[t].cuxiao_money=0,e[t].cuxiaotype=[],e[t].cuxiaonameArr=[],console.log(this.cuxiaoList.info),this.cuxiaoList.info&&this.cuxiaoList.info.length>0){for(var a in this.cuxiaoList.info){var o=this.cuxiaoList.info[a].type;if(console.log(o),1==o||6==o)e[t].cuxiao_money+=-1*this.cuxiaoList.info[a]["money"];else if(2==o)e[t].cuxiao_money+=0;else if(3==o)e[t].cuxiao_money+=this.cuxiaoList.info[a]["money"];else if(4==o||5==o){var r=0;for(var s in this.allbuydata[t].cuxiaolist)this.cuxiaoList.info[a].id==this.allbuydata[t].cuxiaolist[s].id&&(r=this.allbuydata[t].cuxiaolist[s].cuxiaomoney);console.log("cuxiaoMoney"),console.log(r),e[t].cuxiao_money+=-1*r}e[t].cuxiaoid.push(this.cuxiaoList.info[a].id),e[t].cuxiaotype.push(o),e[t].cuxiaonameArr.push(this.cuxiaoList.info[a]["name"])}console.log("allbuydata[bid]"),console.log(e[t])}else{o=this.cuxiaoinfo.info.type;console.log(o),1==o||6==o?e[t].cuxiao_money=-1*this.cuxiaoinfo.info["money"]:2==o?e[t].cuxiao_money=0:3==o?e[t].cuxiao_money=this.cuxiaoinfo.info["money"]:4!=o&&5!=o||(e[t].cuxiao_money=-1*this.cuxiaoinfo.cuxiaomoney),e[t].cuxiaoid=i,e[t].cuxiaotype=o,e[t].cuxiaoname=this.cuxiaoinfo.info["name"]}this.allbuydata=e,this.cuxiaovisible=!1,this.calculatePrice()},showType11List:function(e){this.type11visible=!0,this.bid=e.currentTarget.dataset.bid},changetype11:function(e){this.allbuydata,this.bid;this.type11key=e.currentTarget.dataset.index},chooseType11:function(e){var t=this.allbuydata,a=this.bid,o=this.type11key;if(-1!=o){t[a].type11key=o+1;var r=t[a].freightkey,s=t[a].freightList,n=parseFloat(s[r].type11pricedata[o].price),l=parseFloat(t[a].product_price);1==s[r].freeset&&parseFloat(s[r].free_price)<=l&&(n=0),t[a].freightList[r].freight_price=n,this.allbuydata=t,this.type11visible=!1,this.calculatePrice()}else i.error("请选择物流")},openMendian:function(e){var t=this.allbuydata,a=e.currentTarget.dataset.bid,o=e.currentTarget.dataset.freightkey,r=e.currentTarget.dataset.storekey,s=t[a].freightList[o],n=s.storedata[r];i.goto("mendian?id="+n.id)},openLocation:function(t){var i=this.allbuydata,a=t.currentTarget.dataset.bid,o=t.currentTarget.dataset.freightkey,r=t.currentTarget.dataset.storekey,s=i[a].freightList[o],n=s.storedata[r],l=parseFloat(n.latitude),c=parseFloat(n.longitude),d=n.name;e.openLocation({latitude:l,longitude:c,name:d,scale:13})},editorChooseImage:function(e){var t=this,a=e.currentTarget.dataset.bid,o=e.currentTarget.dataset.idx,r=t.allbuydata[a].editorFormdata;r||(r=[]),i.chooseImage((function(e){r[o]=e[0],t.allbuydata[a].editorFormdata=r,t.test=Math.random()}))},editorBindPickerChange:function(e){var t=e.currentTarget.dataset.bid,i=e.currentTarget.dataset.idx,a=e.detail.value,o=this.allbuydata[t].editorFormdata;o||(o=[]),o[i]=a,this.allbuydata[t].editorFormdata=o,this.test=Math.random()},showMemberList:function(e){this.membervisible=!0},regionchange2:function(e){var t=e.detail.value;this.regiondata=t[0].text+","+t[1].text+","+t[2].text},memberSearch:function(){var e=this;i.post("ApiShop/memberSearch",{diqu:e.regiondata},(function(t){if(i.showLoading(!1),0!=t.status){var a=t.memberList;e.memberList=a}else i.error(t.msg)}))},checkMember:function(e){this.checkMem=e.currentTarget.dataset.info,this.membervisible=!1},showmemberinfo:function(e){var t=this,a=e.currentTarget.dataset.mid;i.showLoading("提交中"),i.post("ApiShop/getmemberuplvinfo",{mid:a},(function(e){i.showLoading(!1),0!=e.status?(t.selectmemberinfo=e.info,t.memberinfovisible=!0):i.error(e.msg)}))},memberinfoClickMask:function(){this.memberinfovisible=!1},doStoreShowAll:function(){this.storeshowall=!0},showglass:function(e){var t=this,a=e.currentTarget.dataset.grid,o=e.currentTarget.dataset.index,r=e.currentTarget.dataset.index2;console.log(a),console.log(o),console.log(r),t.glassrecordlist.length<1?(t.loading,i.post("ApiGlass/myrecord",{pagenum:1,listrow:100},(function(e){t.loading=!1;var i=e.data;console.log(i),t.glassrecordlist=i,t.isshowglass=!0}))):t.isshowglass=!0,t.curindex=o,t.curindex2=r,t.grid=a},hideglass:function(e){this.isshowglass=!1},chooseglass:function(e){var t=e.detail.value,i=this.allbuydata,a=this.grid,o=this.curindex,r=this.curindex2;console.log(t+"-"+this.curindex+"-"+this.curindex2);var s=this.glassrecordlist,n=i[o]["prodata"][r].product,l=s[t].id;a==l?(n.glassrecord={},this.grid=0):(n.glassrecord=s[t],this.grid=s[t].id),this.allbuydata[o]["prodata"][r]["product"]=n,this.isshowglass=!1}}};t.default=a}).call(this,i("df3c")["default"])}},[["8046","common/runtime","common/vendor"]]]);