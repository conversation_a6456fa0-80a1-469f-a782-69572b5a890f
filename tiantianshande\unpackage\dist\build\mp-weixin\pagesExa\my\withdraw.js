require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/my/withdraw"],{5740:function(t,n,e){"use strict";e.r(n);var a=e("6d71"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=i.a},57405:function(t,n,e){"use strict";var a=e("aaf8"),i=e.n(a);i.a},"6d71":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),a={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,userinfo:[],money:0,sysset:!1,paytype:"微信钱包",tmplids:[]}},onLoad:function(t){this.opt=e.getopts(t);this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var n=this;n.loading=!0,e.get("ApiMy/yueWithdraw",{},(function(e){n.loading=!1,t.setNavigationBarTitle({title:"余额转化黑积分"});var a=e.sysset;n.sysset=a,n.tmplids=e.tmplids,n.userinfo=e.userinfo;var i="微信钱包";1==a.withdraw_weixin&&(i="微信钱包"),a.withdraw_weixin&&0!=a.withdraw_weixin||(i="支付宝"),a.withdraw_weixin&&0!=a.withdraw_weixin||a.withdraw_aliaccount&&0!=a.withdraw_aliaccount||(i="银行卡"),n.paytype=i,n.loaded()}))},moneyinput:function(t){var n=parseFloat(this.userinfo.money),a=parseFloat(t.detail.value);a<0?e.error("必须大于0"):a>n&&e.error("可转化"+this.t("余额")+"不足"),this.money=a},changeradio:function(t){var n=t.currentTarget.dataset.paytype;this.paytype=n},formSubmit:function(){var t=this,n=parseFloat(this.userinfo.money),a=(parseFloat(this.sysset.withdrawmin),parseFloat(t.money)),i=this.paytype;isNaN(a)||a<=0?e.error("提现金额必须大于0"):a>n?e.error(t.t("余额")+"不足"):"支付宝"!=i||this.userinfo.aliaccount?"银行卡"!=i||this.userinfo.bankname&&this.userinfo.bankcarduser&&this.userinfo.bankcardnum?(e.showLoading("提交中"),e.post("ApiMy/yueWithdraw",{money:a,paytype:i},(function(n){e.showLoading(!1),0!=n.status?(e.success(n.msg),t.subscribeMessage((function(){setTimeout((function(){e.goto("/pages/money/moneylog")}),1e3)}))):e.error(n.msg)}))):e.alert("请先设置完整银行卡信息",(function(){e.goto("/pagesExa/my/setbankinfo")})):e.alert("请先设置支付宝账号",(function(){e.goto("/pagesExa/my/setaliaccount")}))}}};n.default=a}).call(this,e("df3c")["default"])},7955:function(t,n,e){"use strict";(function(t,n){var a=e("47a9");e("06e9");a(e("3240"));var i=a(e("8443"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},8443:function(t,n,e){"use strict";e.r(n);var a=e("d324"),i=e("5740");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);e("57405");var s=e("828b"),r=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=r.exports},aaf8:function(t,n,e){},d324:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return a}));var a={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},i=function(){var t=this.$createElement,n=(this._self._c,this.isload?this.t("color1"):null),e=this.isload?this.t("余额"):null,a=this.isload?this.t("color1"):null;this.$mp.data=Object.assign({},{$root:{m0:n,m1:e,m2:a}})},o=[]}},[["7955","common/runtime","common/vendor"]]]);