require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/mingpian/favoritelog"],{"07a1":function(t,n,a){"use strict";a.d(n,"b",(function(){return o})),a.d(n,"c",(function(){return i})),a.d(n,"a",(function(){return e}));var e={nomore:function(){return a.e("components/nomore/nomore").then(a.bind(null,"3892"))},nodata:function(){return a.e("components/nodata/nodata").then(a.bind(null,"101c"))},loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},o=function(){var t=this,n=t.$createElement,a=(t._self._c,t.isload?t.__map(t.datalist,(function(n,a){var e=t.__get_orig(n),o=t.dateFormat(n.createtime,"Y-m-d H:i");return{$orig:e,m0:o}})):null);t.$mp.data=Object.assign({},{$root:{l0:a}})},i=[]},"0f5c":function(t,n,a){"use strict";(function(t,n){var e=a("47a9");a("06e9");e(a("3240"));var o=e(a("17058"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},17058:function(t,n,a){"use strict";a.r(n);var e=a("07a1"),o=a("5c36");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return o[t]}))}(i);a("ac07");var u=a("828b"),r=Object(u["a"])(o["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=r.exports},"5c36":function(t,n,a){"use strict";a.r(n);var e=a("f9ba"),o=a.n(e);for(var i in e)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(i);n["default"]=o.a},"5f4c":function(t,n,a){},ac07:function(t,n,a){"use strict";var e=a("5f4c"),o=a.n(e);o.a},f9ba:function(t,n,a){"use strict";(function(t){var e=a("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=e(a("7ca3")),i=getApp(),u={data:function(){var t;return t={opt:{},loading:!1,isload:!1,menuindex:-1},(0,o.default)(t,"isload",!0),(0,o.default)(t,"pagenum",1),(0,o.default)(t,"st",""),(0,o.default)(t,"datalist",[]),(0,o.default)(t,"nomore",!1),(0,o.default)(t,"nodata",!1),t},onLoad:function(t){this.opt=i.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata(!0)},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},methods:{changetab:function(n){var a=n.currentTarget.dataset.st;this.st=a,t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},getdata:function(t){t||(this.pagenum=1,this.datalist=[]);var n=this,a=(n.st,n.pagenum);n.loading=!0,n.nomore=!1,n.nodata=!1,i.post("ApiMingpian/favoritelog",{pagenum:a,id:n.opt.id},(function(t){n.loading=!1;var e=t.data;if(1==a)n.datalist=e,0==e.length&&(n.nodata=!0),n.loaded();else if(0==e.length)n.nomore=!0;else{var o=n.datalist,i=o.concat(e);n.datalist=i}}))}}};n.default=u}).call(this,a("df3c")["default"])}},[["0f5c","common/runtime","common/vendor"]]]);