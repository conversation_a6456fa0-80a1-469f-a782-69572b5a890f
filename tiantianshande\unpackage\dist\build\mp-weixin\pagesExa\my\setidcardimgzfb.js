require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/my/setidcardimgzfb"],{"0636e":function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("06e9");a(n("3240"));var r=a(n("28e9"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"068cc":function(e,t,n){"use strict";var a=n("b310"),r=n.n(a);r.a},1444:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return a}));var a={loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},r=function(){var e=this.$createElement;this._self._c},o=[]},"28e9":function(e,t,n){"use strict";n.r(t);var a=n("1444"),r=n("2b68");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("068cc");var c=n("828b"),i=Object(c["a"])(r["default"],a["b"],a["c"],!1,null,"ff7946c4",null,!1,a["a"],void 0);t["default"]=i.exports},"2b68":function(e,t,n){"use strict";n.r(t);var a=n("7faa"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=r.a},"7faa":function(e,t,n){"use strict";(function(e){var a=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("6370")),o=getApp(),c={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,realname:"",idcard:"",textset:{},haspwd:0,idcard_front:"../../static/img/upload.png",idcard_back:"../../static/img/upload.png",showposter:!1,orderNumber:"",cert_url:"",cert_type:""}},onLoad:function(e){this.loaded(),this.getInfo()},onPullDownRefresh:function(){},methods:{getInfo:function(){var t=this;o.post("ApiFace/info",{},(function(n){1==n.status?(t.realname=n.data.name,t.idcard=n.data.id_number,t.idcard_front=n.data.img_front,t.idcard_back=n.data.img_reverse,t.orderNumber=n.data.order_num,t.cert_type=n.data.cert_type,t.orderNumber&&(t.showposter=!0,r.default.make({canvasId:"qrcode",componentInstance:t,text:n.data.cert_url,size:250,margin:0,backgroundColor:"#ffffff",foregroundColor:"#000000",fileType:"jpg",errorCorrectLevel:r.default.errorCorrectLevel.H})),1==n.data.status&&e.showModal({content:"您已成功认证",showCancel:!1,success:function(){e.navigateBack()}})):o.alert(n.msg)}))},baocun:function(){e.canvasToTempFilePath({canvasId:"qrcode",success:function(t){console.log(t),e.previewImage({urls:[t.tempFilePath],current:0,success:function(e){console.log("预览图片成功")}})},fail:function(e){console.log(e)}})},renzheng:function(){""!=this.orderNumber?o.post("ApiFace/query",{log_id:this.orderNumber},(function(t){e.showModal({content:t.msg,showCancel:!1,success:function(){e.navigateBack()}})})):o.alert("请先提交认证信息")},posterDialogClose:function(){this.showposter=!1},upIdcardHead:function(){var e=this;o.chooseImage((function(t){e.idcard_front=t[0]}))},upIdcardBack:function(){var e=this;o.chooseImage((function(t){e.idcard_back=t[0]}))},formSubmit:function(){var e=this,t=this.realname,n=this.idcard,a=this.idcard_front,c=this.idcard_back;this.is_eid_verify;""!=t?""!=n?"../../static/img/upload.png"!=a?"../../static/img/upload.png"!=c?o.post("ApiFace/detect",{name:t,card_id:n,type:"ZHIMACREDIT",img_front:a,img_reverse:c},(function(t){1==t.status?(e.orderNumber=t.data.orderNumber,e.showposter=!0,r.default.make({canvasId:"qrcode",componentInstance:e,text:t.data.originalUrl,size:250,margin:0,backgroundColor:"#ffffff",foregroundColor:"#000000",fileType:"jpg",errorCorrectLevel:r.default.errorCorrectLevel.H})):o.error(t.msg)})):o.alert("请上传身份证背面"):o.alert("请上传身份证正面"):o.alert("请输入身份证号码"):o.alert("请输入姓名")}}};t.default=c}).call(this,n("df3c")["default"])},b310:function(e,t,n){}},[["0636e","common/runtime","common/vendor","pagesExa/common/vendor"]]]);