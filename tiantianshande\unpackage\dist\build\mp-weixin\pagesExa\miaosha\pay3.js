require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/miaosha/pay3"],{"0e1e":function(o,e,t){"use strict";(function(o,t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=getApp(),n={data:function(){return{opt:{},outmember:{},loading:!1,isload:!1,menuindex:-1,pre_url:i.globalData.pre_url,detailurl:"",tourl:"",typeid:"0",wxpay:0,wxpay_type:0,alipay:0,baidupay:0,toutiaopay:0,moneypay:0,cancod:0,daifu:0,daifu_txt:"好友代付",pay_month:0,pay_transfer:0,codtxt:"",pay_month_txt:"",give_coupon_list:[],give_coupon_num:0,userinfo:[],paypwd:"",hiddenmodalput:!0,payorder:{},tmplids:[],give_coupon_show:!1,give_coupon_close_url:"",more_alipay:0,alipay2:0,alipay3:0,paypal:0,payimg:"",yuanbao_money:0,total_yuanbao:0,yuanbao_msg:"",yuanbaopay:0,open_pay:!1,pay_type:"",invite_free:"",invite_status:!1,free_tmplids:"",sharepic:i.globalData.initdata.logo}},onLoad:function(o){this.opt=i.getopts(o),console.log(this.opt),this.opt.tourl&&(this.tourl=decodeURIComponent(this.opt.tourl)),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var o=this;o.loading=!0;var e="";"mp"!=i.globalData.platform&&"h5"!=i.globalData.platform||(e=location.href),i.post("ApiMiaosha/pay3",{changciid:o.opt.id,thisurl:e,tourl:o.tourl,scene:i.globalData.scene},(function(e){if(o.loading=!1,0!=e.status){if(o.datalist=e.datalist,o.wxpay=e.wxpay,o.outmember=e.outmember,o.wxpay_type=e.wxpay_type,o.alipay=e.alipay,o.baidupay=e.baidupay,o.toutiaopay=e.toutiaopay,o.cancod=e.cancod,o.codtxt=e.codtxt,o.daifu=e.daifu,o.daifu_txt=e.daifu_txt,o.pay_money=e.pay_money,o.pay_money_txt=e.pay_money_txt,o.moneypay=e.moneypay,o.pay_transfer=e.pay_transfer,o.pay_transfer_info=e.pay_transfer_info,o.pay_month=e.pay_month,o.pay_month_txt=e.pay_month_txt,o.payorder=e.payorder,o.userinfo=e.userinfo,o.tmplids=e.tmplids,o.give_coupon_list=e.give_coupon_list,o.give_coupon_list)for(var t in o.give_coupon_list)o.give_coupon_num+=o.give_coupon_list[t]["givenum"];o.detailurl=e.detailurl,o.tourl=e.tourl,o.paypal=e.paypal||0,o.more_alipay=e.more_alipay,o.alipay2=e.alipay2,o.alipay3=e.alipay3,o.yuanbao_money=e.yuanbao_money,o.total_yuanbao=e.total_yuanbao,o.yuanbao_msg=e.yuanbao_msg,o.yuanbaopay=e.yuanbaopay,o.wxpay?22==o.wxpay_type?o.typeid=22:o.typeid=2:o.moneypay?o.typeid=1:o.alipay?(o.typeid=3,2==o.alipay&&(o.typeid=23)):o.more_alipay?(o.alipay2&&(o.typeid=31),o.alipay3&&(o.typeid=32)):o.baidupay?o.typeid=11:o.toutiaopay&&(o.typeid=12),e.invite_free&&(o.invite_free=e.invite_free),e.free_tmplids&&(o.free_tmplids=e.free_tmplids),o.loaded(),o.opt&&"success"==o.opt.paypal&&(o.typeid=51,i.showLoading("支付中"),i.post("ApiPay/paypalRedirect",{orderid:o.opt.id,paymentId:o.opt.paymentId,PayerID:o.opt.PayerID},(function(e){i.showLoading(!1),1==e.status?(i.success(e.msg),o.subscribeMessage((function(){o.invite_free?o.invite_status=!0:setTimeout((function(){o.give_coupon_list&&o.give_coupon_list.length>0?(o.give_coupon_show=!0,o.give_coupon_close_url=o.tourl):o.gotourl(o.tourl,"reLaunch")}),1e3)}))):0==e.status&&i.error(e.msg)})))}else i.error(e.msg)}))},previewImage:function(e){o.previewImage({current:e,urls:[e]})},changeradio:function(o){var e=o.currentTarget.dataset.typeid;this.typeid=e,console.log(e)},uploadpayimg:function(o){var e=this;i.chooseImage((function(t){e.payimg=t[0],i.post("ApiMiaosha/shangchuan",{payimg:e.payimg,id:o},(function(o){i.error(o.msg)}))}),1)},queren:function(o){i.confirm("确定要收款吗?",(function(){i.showLoading("提交中"),i.post("ApiMiaosha/queren",{id:o},(function(o){i.showLoading(!1),i.error(o.msg)}))}))},fanhui:function(){t.navigateTo({url:"fukuan?id="+this.opt.id})},topay:function(e){var n=this,a=n.typeid,s=this.payorder.id;if(1==a){if(n.userinfo.haspwd&&""==n.paypwd)return void n.$refs.dialogInput.open();i.confirm("确定已经支付吗?",(function(){i.showLoading("提交中"),i.post("ApiMiaosha/pay",{op:"submit",orderid:s,typeid:5,paypwd:n.paypwd,pay_type:n.pay_type},(function(o){if(i.showLoading(!1),i.error(o.msg),0!=o.status)return 2==o.status?(i.success(o.msg),void n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}))):void 0;i.error(o.msg)}))}))}else if(2==a)console.log(i),i.showLoading("提交中"),i.post("ApiPay/pay",{op:"submit",orderid:s,typeid:a},(function(e){if(i.showLoading(!1),0!=e.status){if(2==e.status)return i.success(e.msg),void n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}));var a=e.data;"wx"==i.globalData.platform?"shop"==n.payorder.type||2==n.wxpay_type?a.orderInfo?(console.log("requestOrderPayment1"),o.requestOrderPayment({timeStamp:a.timeStamp,nonceStr:a.nonceStr,package:a.package,signType:a.signType?a.signType:"MD5",paySign:a.paySign,orderInfo:a.orderInfo,success:function(o){i.success("付款完成"),n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}))},fail:function(o){}})):(console.log("requestOrderPayment2"),o.requestOrderPayment({timeStamp:a.timeStamp,nonceStr:a.nonceStr,package:a.package,signType:a.signType?a.signType:"MD5",paySign:a.paySign,success:function(o){i.success("付款完成"),n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}))},fail:function(o){}})):t.requestPayment({provider:"wxpay",timeStamp:a.timeStamp,nonceStr:a.nonceStr,package:a.package,signType:a.signType?a.signType:"MD5",paySign:a.paySign,success:function(o){i.success("付款完成"),n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}))},fail:function(o){}}):"mp"==i.globalData.platform||("h5"==i.globalData.platform?location.href=a.wx_url+"&redirect_url="+encodeURIComponent(location.href.split("#")[0]+"#"+n.tourl):"app"==i.globalData.platform?(console.log(a),t.requestPayment({provider:"wxpay",orderInfo:a,success:function(o){i.success("付款完成"),n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}))},fail:function(o){console.log(o)}})):"qq"==i.globalData.platform&&qq.requestWxPayment({url:a.wx_url,referer:a.referer,success:function(o){n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}))},fail:function(o){}}))}else i.error(e.msg)}));else if(3==a||31==a||32==a)i.showLoading("提交中"),i.post("ApiPay/pay",{op:"submit",orderid:s,typeid:a},(function(o){if(console.log(o),i.showLoading(!1),0!=o.status){if(2==o.status)return i.success(o.msg),void n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}));var e=o.data;"alipay"==i.globalData.platform?t.requestPayment({provider:"alipay",orderInfo:e.trade_no,success:function(o){console.log(o),"6001"!=o.resultCode&&(i.success("付款完成"),n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)})))},fail:function(o){}}):"mp"==i.globalData.platform||"h5"==i.globalData.platform?(document.body.innerHTML=o.data,document.forms["alipaysubmit"].submit()):"app"==i.globalData.platform&&(console.log("------------alipay----------"),console.log(e),console.log("------------alipay end----------"),t.requestPayment({provider:"alipay",orderInfo:e,success:function(o){console.log("------------success----------"),console.log(o),i.success("付款完成"),n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}))},fail:function(o){console.log(o)}}))}else i.error(o.msg)}));else if("11"==a)i.showLoading("提交中"),i.post("ApiPay/pay",{op:"submit",orderid:s,typeid:a},(function(o){i.showLoading(!1),swan.requestPolymerPayment({orderInfo:o.orderInfo,success:function(o){i.success("付款完成"),n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}))},fail:function(o){2!=o.errCode&&i.alert(JSON.stringify(o))}})}));else if("12"==a)i.showLoading("提交中"),i.post("ApiPay/pay",{op:"submit",orderid:s,typeid:a},(function(o){i.showLoading(!1),console.log(o.orderInfo),tt.pay({service:5,orderInfo:o.orderInfo,success:function(o){0===o.code&&(i.success("付款完成"),n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)})))},fail:function(o){i.alert(JSON.stringify(o))}})}));else if("22"==a)if("wx"==i.globalData.platform)o.login({success:function(e){e.code?(i.showLoading("提交中"),i.post("ApiPay/getYunMpauthParams",{jscode:e.code},(function(e){i.showLoading(!1),i.post("https://showmoney.cn/scanpay/fixed/mpauth",e.params,(function(e){console.log(e.sessionKey),i.post("ApiPay/getYunUnifiedParams",{orderid:s,sessionKey:e.sessionKey},(function(e){i.post("https://showmoney.cn/scanpay/unified",e.params,(function(e){"09"==e.respcd?o.requestPayment({timeStamp:e.timeStamp,nonceStr:e.nonceStr,package:e.package,signType:e.mpSignType,paySign:e.mpSign,success:function(o){i.success("付款完成"),n.subscribeMessage((function(){n.invite_free?n.invite_status=!0:setTimeout((function(){n.give_coupon_list&&n.give_coupon_list.length>0?(n.give_coupon_show=!0,n.give_coupon_close_url=n.tourl):n.gotourl(n.tourl,"reLaunch")}),1e3)}))},fail:function(o){}}):i.alert(e.errorDetail)}))}))}))}))):console.log("登录失败！"+e.errMsg)}});else{var u=i.globalData.baseurl+"ApiPay/pay&aid="+i.globalData.aid+"&platform="+i.globalData.platform+"&session_id="+i.globalData.session_id;u+="&op=submit&orderid="+s+"&typeid=22",location.href=u}else{if("23"==a)return setTimeout((function(){n.$refs.dialogPayconfirm.open()}),1e3),void i.goto("/pages/index/webView2?orderid="+s+"&typeid=23&aid="+i.globalData.aid+"&platform="+i.globalData.platform+"&session_id="+i.globalData.session_id);if("24"==a)return void i.goto("/pages/index/webView2?orderid="+s+"&typeid=24");if("yuanbao"==a){console.log(i);var r=n.total_yuanbao-0,p=n.userinfo.yuanbao-0;if(r>p)return void i.alert(n.t("元宝")+"不足");n.open_pay=!0,n.pay_type="yuanbao"}else"51"==a&&(i.showLoading("提交中"),i.post("ApiPay/pay",{op:"submit",orderid:s,typeid:a},(function(o){if(i.showLoading(!1),console.log(o),1==o.status)if("app"==i.globalData.platform){var e=plus.webview.create("","custom-webview",{top:t.getSystemInfoSync().statusBarHeight+44});e.loadURL(o.data);var a=n.$scope.$getAppWebview();a.append(e)}else i.goto("url::"+o.data);else i.alert(o.msg)})))}},topay2:function(){var o=this,e=this.payorder.id;i.confirm("确定要"+o.codtxt+"吗?",(function(){i.showLoading("提交中"),i.post("ApiPay/pay",{op:"submit",orderid:e,typeid:4},(function(e){if(i.showLoading(!1),0!=e.status)return 2==e.status?(i.success(e.msg),void o.subscribeMessage((function(){setTimeout((function(){o.gotourl(o.tourl,"reLaunch")}),1e3)}))):void 0;i.error(e.msg)}))}))},topayMonth:function(){var o=this,e=this.payorder.id;i.confirm("确定要"+o.pay_month_txt+"支付吗?",(function(){i.showLoading("提交中"),i.post("ApiPay/pay",{op:"submit",orderid:e,typeid:41},(function(e){if(i.showLoading(!1),0!=e.status)return 2==e.status?(i.success(e.msg),void o.subscribeMessage((function(){setTimeout((function(){o.gotourl(o.tourl,"reLaunch")}),1e3)}))):void 0;i.error(e.msg)}))}))},topayTransfer:function(o){var e=this.payorder.id;i.goto("/pagesExa/miaosha/payshangchuan?id="+e)},give_coupon_close:function(o){var e=o.currentTarget.dataset.url;this.give_coupon_show=!1,this.gotourl(e,"reLaunch")},gotourl:function(o,e){if(("mp"==i.globalData.platform||"h5"==i.globalData.platform)&&0===o.indexOf("miniProgram::")){o=o.slice(13);var t=o.split("|");return console.log(t),void this.showOpenWeapp()}i.goto(o,e)},showOpenWeapp:function(){this.$refs.dialogOpenWeapp.open()},closeOpenWeapp:function(){this.$refs.dialogOpenWeapp.close()},PayconfirmFun:function(){this.gotourl(this.tourl,"reLaunch")},close_pay:function(){this.open_pay=!1},closeInvite:function(){var o=this;o.invite_status=!1,setTimeout((function(){o.give_coupon_list&&o.give_coupon_list.length>0?(o.give_coupon_show=!0,o.give_coupon_close_url=o.tourl):o.gotourl(o.tourl,"reLaunch")}),1e3)},copybankcarduser:function(e){var t=e.currentTarget.dataset.bankcarduser;o.setClipboardData({data:t,success:function(e){o.showToast({title:"复制成功"})}})},copybankcardnum:function(e){var t=e.currentTarget.dataset.bankcardnum;o.setClipboardData({data:t,success:function(e){o.showToast({title:"复制成功"})}})},gotoInvite:function(){var o=this.free_tmplids;o&&o.length>0&&t.requestSubscribeMessage({tmplIds:o,success:function(o){console.log(o)},fail:function(o){console.log(o)}}),i.goto("/pagesExt/invite_free/index","reLaunch")},todaifu:function(o){var e=this,n=i.getplatform();e.payorder.id;if("mp"==n||"h5"==n){var a=i.globalData.pre_url+"/h5/"+i.globalData.aid+".html#/pages/pay/daifu?scene=id_"+e.payorder.id;this._sharemp({title:"您有一份好友代付待查收，请尽快处理~",link:a,pic:e.sharepic}),i.error("点击右上角发送给好友或分享到朋友圈")}else"app"==n?t.showActionSheet({itemList:["发送给微信好友","分享到微信朋友圈"],success:function(o){if(o.tapIndex>=0){var n="WXSceneSession";1==o.tapIndex&&(n="WXSenceTimeline");var a={provider:"weixin",type:0};a.scene=n,a.title="您的好友向您发出了代付请求",a.summary="您有一份好友代付待查收，请尽快处理~",a.href=i.globalData.pre_url+"/h5/"+i.globalData.aid+".html#/pages/pay/daifu?scene=id_"+e.payorder.id,a.imageUrl="",t.share(a)}}}):i.error("该终端不支持此操作")}}};e.default=n}).call(this,t("3223")["default"],t("df3c")["default"])},"4c17":function(o,e,t){},"4f0b":function(o,e,t){"use strict";t.r(e);var i=t("c402"),n=t("b335");for(var a in n)["default"].indexOf(a)<0&&function(o){t.d(e,o,(function(){return n[o]}))}(a);t("c0b6");var s=t("828b"),u=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=u.exports},b335:function(o,e,t){"use strict";t.r(e);var i=t("0e1e"),n=t.n(i);for(var a in i)["default"].indexOf(a)<0&&function(o){t.d(e,o,(function(){return i[o]}))}(a);e["default"]=n.a},c0b6:function(o,e,t){"use strict";var i=t("4c17"),n=t.n(i);n.a},c402:function(o,e,t){"use strict";t.d(e,"b",(function(){return n})),t.d(e,"c",(function(){return a})),t.d(e,"a",(function(){return i}));var i={loading:function(){return t.e("components/loading/loading").then(t.bind(null,"ceaa"))},dpTabbar:function(){return t.e("components/dp-tabbar/dp-tabbar").then(t.bind(null,"b875"))},popmsg:function(){return t.e("components/popmsg/popmsg").then(t.bind(null,"2bf2"))}},n=function(){var o=this.$createElement;this._self._c},a=[]},cb11:function(o,e,t){"use strict";(function(o,e){var i=t("47a9");t("06e9");i(t("3240"));var n=i(t("4f0b"));o.__webpack_require_UNI_MP_PLUGIN__=t,e(n.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])}},[["cb11","common/runtime","common/vendor"]]]);