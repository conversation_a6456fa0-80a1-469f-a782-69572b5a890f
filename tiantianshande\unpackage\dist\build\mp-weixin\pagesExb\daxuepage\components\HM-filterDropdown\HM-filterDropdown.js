require('../../../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown"],{"022a":function(e,t,n){"use strict";n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){}));var u=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__map(e.subData,(function(t,n){var u=e.__get_orig(t),s=("hierarchy"==t.type||"hierarchy-column"==t.type)&&t.submenu.length>0,i=s?e.activeMenuArr[n].length:null,a=s?e.activeMenuArr[n].length:null,r=s&&"hierarchy"==t.type?e.__map(t.submenu,(function(t,u){var s=e.__get_orig(t),i=e.activeMenuArr[n][0]==u&&t.submenu.length>0,a=i?e.__map(t.submenu,(function(n,u){var s=e.__get_orig(n),i=n.submenu&&t.submenu.length>0&&n.submenu.length>0,a=i?e.__map(n.submenu,(function(t,u){var s=e.__get_orig(t),i=1!=n.showAllSub&&8==u&&n.submenu.length>9;return{$orig:s,g5:i}})):null;return{$orig:s,g4:i,l0:a}})):null;return{$orig:s,g3:i,l1:a}})):null,h=s&&"hierarchy"!=t.type&&"hierarchy-column"==t.type?e.__map(t.submenu,(function(t,u){var s=e.__get_orig(t),i=e.activeMenuArr[n][0]==u&&t.submenu.length>0;return{$orig:s,g6:i}})):null,l=s&&"hierarchy"!=t.type&&"hierarchy-column"==t.type?e.__map(t.submenu,(function(t,u){var s=e.__get_orig(t),i=e.__map(t.submenu,(function(t,s){var i=e.__get_orig(t),a=e.activeMenuArr[n][0]==u&&e.activeMenuArr[n][1]==s&&t.submenu.length>0;return{$orig:i,g7:a}}));return{$orig:s,l4:i}})):null;return{$orig:u,g0:s,g1:i,g2:a,l2:r,l3:h,l5:l}})));e.$mp.data=Object.assign({},{$root:{l6:n}})},s=[]},"16d6":function(e,t,n){"use strict";n.r(t);var u=n("f91c"),s=n.n(u);for(var i in u)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return u[e]}))}(i);t["default"]=s.a},"696b":function(e,t,n){"use strict";var u=n("c4c0"),s=n.n(u);s.a},b1ea:function(e,t,n){"use strict";n.r(t);var u=n("022a"),s=n("16d6");for(var i in s)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return s[e]}))}(i);n("696b");var a=n("828b"),r=Object(a["a"])(s["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);t["default"]=r.exports},c4c0:function(e,t,n){},f91c:function(e,t,n){"use strict";var u=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=u(n("3b2d")),i=u(n("af34")),a={name:"HM-filterDropdown",data:function(){return{menuData:[],subData:[],menu:[],showPage:-1,pageState:[],activeMenuArr:[],shadowActiveMenuArr:[],defaultActive:[],triangleDeg:[],isShowMask:!1,maskVisibility:!1,firstScrollInto:0,secondScrollInto:0,thirdScrollInto:0,componentTop:0,isReadNewSelect:!1}},props:{menuTop:{value:Number,default:!1},filterData:{value:Array,default:[]},defaultSelected:{value:Array,default:[]},updateMenuName:{value:Boolean,default:!0},dataFormat:{value:String,default:"Array"}},watch:{filterData:{handler:function(e){console.log("watch filterData"),this.menuData=JSON.parse(JSON.stringify(e)),this.initMenu()},immediate:!0,deep:!0},defaultSelected:function(e){0!=e.length&&(this.defaultActive=JSON.parse(JSON.stringify(e)),this.activeMenuArr=JSON.parse(JSON.stringify(e)),this.shadowActiveMenuArr=JSON.parse(JSON.stringify(e)),this.updateMenuName&&this.setMenuName())}},methods:{initMenu:function(){for(var e,t=[],n=[],u=0;u<this.menuData.length;u++){var s=this.menuData[u];n.push({name:s.name||("filter"==s.type?"筛选":s.submenu[0].name),type:s.type}),t.push(this.processActive(s)),this.triangleDeg.push(0),this.pageState.push(!1),s=this.processSubMenu(s),this.menuData[u]=s}this.menu=n,t=this.defaultActive.length>0?this.defaultActive:this.activeMenuArr.length>0?this.activeMenuArr:t,this.defaultActive=[],(e=this.activeMenuArr).splice.apply(e,[0,this.activeMenuArr.length].concat((0,i.default)(JSON.parse(JSON.stringify(t))))),this.shadowActiveMenuArr=JSON.parse(JSON.stringify(t)),this.subData=this.menuData,this.updateMenuName&&this.setMenuName()},setMenuName:function(){for(var e=0;e<this.activeMenuArr.length;e++){var t=this.activeMenuArr[e];if("hierarchy"==this.subData[e].type||"hierarchy-column"==this.subData[e].type)if("number"==typeof t[0]){var n=this.subData[e].submenu[t[0]];t.length>1&&(n=n.submenu[t[1]]||n,t.length>2&&(n=n.submenu[t[2]]||n)),this.menu[e].name=n.name}else this.menu[e].name=this.subData[e].name}},showMoreSub:function(e){this.subData[this.showPage].submenu[this.activeMenuArr[this.showPage][0]].submenu[e].showAllSub=!0,this.$forceUpdate()},selectHierarchyMenu:function(e,t,n,u){var s;console.log("selectHierarchyMenu"),null==n&&null==u&&this.shadowActiveMenuArr[e].length>0&&this.shadowActiveMenuArr[e][0]==t&&this.activeMenuArr.splice(e,1,JSON.parse(JSON.stringify(this.shadowActiveMenuArr[e])));var a=new Array(this.activeMenuArr[e].length).fill(null);(s=this.activeMenuArr[e]).splice.apply(s,[0,this.activeMenuArr[e].length].concat((0,i.default)(a))),this.activeMenuArr[e].splice(0,1,t);var r=this.subData[e].submenu[t];0==r.submenu.length?this.selectedMemu(e,t,n,u):null!=n&&(this.activeMenuArr[e].splice(1,1,n),r=r.submenu[n],0==r.submenu.length||"hierarchy"==this.menu[e].type&&null==u?this.selectedMemu(e,t,n,u):null!=u&&(this.activeMenuArr[e].splice(2,1,u),r=r.submenu[u],this.selectedMemu(e,t,n,u)))},selectedMemu:function(e,t,n,u){var s=this.subData[e].submenu[t].submenu[n];this.updateMenuName&&(this.menu[e].name=null!=u&&s.submenu[u].name||null!=n&&s.name||this.subData[e].submenu[t].name),this.shadowActiveMenuArr[e]=JSON.parse(JSON.stringify(this.activeMenuArr[e])),this.hideMenu(!0)},setFilterData:function(e){this.shadowActiveMenuArr[e]=JSON.parse(JSON.stringify(this.activeMenuArr[e])),this.hideMenu(!0)},resetFilterData:function(e){var t=[],n=this.shadowActiveMenuArr[e].length;while(n>0){t.push([]);for(var u=this.subData[e].submenu[n-1].submenu,s=0;s<u.length;s++)this.subData[e].submenu[n-1].submenu[s].selected=!1;n--}this.activeMenuArr[e]=JSON.parse(JSON.stringify(t)),this.$forceUpdate()},selectFilterLabel:function(e,t,n){var u=this.activeMenuArr[e][t].indexOf(n);u>-1?(this.activeMenuArr[e][t].splice(u,1),this.subData[e].submenu[t].submenu[n].selected=!1):(this.activeMenuArr[e][t].push(n),this.subData[e].submenu[t].submenu[n].selected=!0),this.$forceUpdate()},selectRadioLabel:function(e,t,n){var u=this.activeMenuArr[e][t][0];u==n?(this.subData[e].submenu[t].submenu[u].selected=!1,this.activeMenuArr[e][t][0]=null):(null!=u&&u<this.subData[e].submenu[t].submenu.length&&(this.subData[e].submenu[t].submenu[u].selected=!1),this.subData[e].submenu[t].submenu[n].selected=!0,this.activeMenuArr[e][t][0]=n),this.$forceUpdate()},togglePage:function(e){var t=this;this.isToggleing||(this.isToggleing=!0,e==this.showPage?this.hideMenu():this.showMenu(e),setTimeout((function(){t.isToggleing=!1}),150))},hideMenu:function(e){this.hideMenuLayer(!0),this.hideMaskLayer(),this.showPage=-1,e&&this.confirm()},showMenu:function(e){this.showPage>-1&&this.hideMenuLayer(!1),this.showMenuLayer(e),this.showMaskLayer()},hideMaskLayer:function(){var e=this;this.isShowMask=!1,setTimeout((function(){e.maskVisibility=!1}),200)},showMaskLayer:function(){var e=this;this.maskVisibility=!0,this.$nextTick((function(){setTimeout((function(){e.isShowMask=!0}),0)}))},hideMenuLayer:function(e){var t=this;this.triangleDeg[this.showPage]=0;var n=this.showPage;e?setTimeout((function(){t.pageState.splice(n,1,!1)}),200):this.pageState.splice(n,1,!1),this.firstScrollInto=null,this.secondScrollInto=null},showMenuLayer:function(e){var t=this;this.processPage(e),this.pageState.splice(e,1,!0),this.$nextTick((function(){setTimeout((function(){t.showPage=e}),0)})),this.triangleDeg[e]=180},confirm:function(){var e=this,t=JSON.parse(JSON.stringify(this.shadowActiveMenuArr)),n=JSON.parse(JSON.stringify(this.shadowActiveMenuArr));t.forEach((function(u,i){if("object"==(0,s.default)(u[0]))u.forEach((function(s,a){null!=s&&(s.sort((function(e,t){return e-t})),u[a]=s,s.forEach((function(u,s){n[i][a][s]=null==u||u>=e.subData[i].submenu[a].submenu.length?null:e.subData[i].submenu[a].submenu[u].value,"radio"==e.subData[i].type&&null==n[i][a][s]&&(n[i][a]=[],t[i][a]=[])})))}));else{var a=e.subData[i].submenu[u[0]];n[i][0]=a.value,n[i].length>=2&&null!=u[1]&&(a.submenu.length>0?(a=a.submenu[u[1]],n[i][1]=a.hasOwnProperty("value")?a.value:null):n[i][1]=null,n[i].length>=3&&null!=u[2]&&(a.submenu.length>0?(a=a.submenu[u[2]],n[i][2]=a.hasOwnProperty("value")?a.value:null):n[i][2]=null))}t[i]=u})),this.$emit("confirm",{index:t,value:n})},reloadActiveMenuArr:function(){for(var e=0;e<this.menuData.length;e++){var t=this.menuData[e],n=this.processActive(t);t=this.processSubMenu(t),this.activeMenuArr[e].length!=n.length&&(this.menuData[e]=t,this.activeMenuArr.splice(e,1,JSON.parse(JSON.stringify(n))),this.shadowActiveMenuArr.splice(e,1,JSON.parse(JSON.stringify(n))))}this.subData=this.menuData,this.$forceUpdate()},processPage:function(e){var t=this;if(this.reloadActiveMenuArr(),this.activeMenuArr.splice(e,1,JSON.parse(JSON.stringify(this.shadowActiveMenuArr[e]))),"filter"==this.menu[e].type)for(var n=this.shadowActiveMenuArr[e].length,u=0;u<n;u++)for(var s=this.subData[e].submenu[u].submenu,i=0;i<s.length;i++)this.shadowActiveMenuArr[e][u].indexOf(i)>-1?this.subData[e].submenu[u].submenu[i].selected=!0:this.subData[e].submenu[u].submenu[i].selected=!1;else if("hierarchy"==this.menu[e].type)this.$nextTick((function(){setTimeout((function(){t.firstScrollInto=parseInt(t.activeMenuArr[e][0]),t.secondScrollInto=parseInt(t.activeMenuArr[e][1])}),0)}));else if("hierarchy-column"==this.menu[e].type)this.$nextTick((function(){setTimeout((function(){t.firstScrollInto=parseInt(t.activeMenuArr[e][0]),t.secondScrollInto=parseInt(t.activeMenuArr[e][1]),t.thirdScrollInto=parseInt(t.activeMenuArr[e][2])}),0)}));else if("radio"==this.menu[e].type)for(var a=this.shadowActiveMenuArr[e].length,r=0;r<a;r++)for(var h=this.subData[e].submenu[r].submenu,l=0;l<h.length;l++)this.shadowActiveMenuArr[e][r].indexOf(l)>-1?this.subData[e].submenu[r].submenu[l].selected=!0:this.subData[e].submenu[r].submenu[l].selected=!1},processActive:function(e){var t=[];if(("hierarchy"==e.type||"hierarchy-column"==e.type)&&e.hasOwnProperty("submenu")&&e.submenu.length>0){var n=this.getMaxFloor(e.submenu);while(n>0)t.push(null),n--}else if("filter"==e.type){var u=e.submenu.length;while(u>0)t.push([]),u--}else if("radio"==e.type){var s=e.submenu.length;while(s>0)t.push([]),s--}return t},processSubMenu:function(e){if(e.hasOwnProperty("submenu")&&e.submenu.length>0)for(var t=0;t<e.submenu.length;t++)e.submenu[t]=this.processSubMenu(e.submenu[t]);else e.submenu=[];return e},getMaxFloor:function(e){var t=0;return function e(n,u){n.forEach((function(n){t=u>t?u:t,n.hasOwnProperty("submenu")&&n.submenu.length>0&&e(n.submenu,u+1)}))}(e,1),t},discard:function(){}}};t.default=a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown-create-component',
    {
        'pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("b1ea"))
        })
    },
    [['pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown-create-component']]
]);
