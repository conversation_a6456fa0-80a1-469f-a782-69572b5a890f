require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/my/setbankinfo"],{"06b8":function(n,t,a){"use strict";(function(n,t){var e=a("47a9");a("06e9");e(a("3240"));var o=e(a("9dc0d"));n.__webpack_require_UNI_MP_PLUGIN__=a,t(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},3952:function(n,t,a){"use strict";a.r(t);var e=a("d822"),o=a.n(e);for(var i in e)["default"].indexOf(i)<0&&function(n){a.d(t,n,(function(){return e[n]}))}(i);t["default"]=o.a},"7a49":function(n,t,a){},"7fda":function(n,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return e}));var e={loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},o=function(){var n=this.$createElement,t=(this._self._c,this.isload?this.t("color1"):null),a=this.isload?this.t("color1rgb"):null;this.$mp.data=Object.assign({},{$root:{m0:t,m1:a}})},i=[]},"9dc0d":function(n,t,a){"use strict";a.r(t);var e=a("7fda"),o=a("3952");for(var i in o)["default"].indexOf(i)<0&&function(n){a.d(t,n,(function(){return o[n]}))}(i);a("b943");var u=a("828b"),s=Object(u["a"])(o["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);t["default"]=s.exports},b943:function(n,t,a){"use strict";var e=a("7a49"),o=a.n(e);o.a},d822:function(n,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,banklist:["工商银行","农业银行","中国银行","建设银行","招商银行","邮储银行","交通银行","浦发银行","民生银行","兴业银行","平安银行","中信银行","华夏银行","广发银行","光大银行","北京银行","宁波银行"],bankname:"",userinfo:{},textset:{}}},onLoad:function(n){this.opt=e.getopts(n),this.isload=!0,this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var n=this;n.loading=!0,e.get("ApiMy/set",{},(function(t){n.loading=!1,n.userinfo=t.userinfo,n.bankname=t.userinfo.bankname,n.loaded()}))},formSubmit:function(n){var t=n.detail.value,a=this.bankname,o=t.bankcarduser,i=t.bankcardnum,u=t.bankaddress;""!=a?(e.showLoading("提交中"),e.post("ApiMy/setfield",{bankname:a,bankaddress:u,bankcarduser:o,bankcardnum:i},(function(n){e.showLoading(!1),1==n.status?(e.success(n.msg),setTimeout((function(){e.goback(!0)}),1e3)):e.error(n.msg)}))):e.alert("请选择开户行")},bindBanknameChange:function(n){this.bankname=this.banklist[n.detail.value]}}};t.default=o}},[["06b8","common/runtime","common/vendor"]]]);