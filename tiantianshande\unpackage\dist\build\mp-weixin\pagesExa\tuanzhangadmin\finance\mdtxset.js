require('../../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/tuanzhangadmin/finance/mdtxset"],{"0738":function(n,t,a){"use strict";(function(n,t){var e=a("47a9");a("06e9");e(a("3240"));var i=e(a("7997"));n.__webpack_require_UNI_MP_PLUGIN__=a,t(i.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"47d7":function(n,t,a){"use strict";a.r(t);var e=a("ad94"),i=a.n(e);for(var o in e)["default"].indexOf(o)<0&&function(n){a.d(t,n,(function(){return e[n]}))}(o);t["default"]=i.a},"52f9":function(n,t,a){"use strict";var e=a("df5a"),i=a.n(e);i.a},6268:function(n,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return e}));var e={loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},i=function(){var n=this.$createElement;this._self._c},o=[]},7997:function(n,t,a){"use strict";a.r(t);var e=a("6268"),i=a("47d7");for(var o in i)["default"].indexOf(o)<0&&function(n){a.d(t,n,(function(){return i[n]}))}(o);a("52f9");var u=a("828b"),c=Object(u["a"])(i["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);t["default"]=c.exports},ad94:function(n,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,smsdjs:"",banklist:["工商银行","农业银行","中国银行","建设银行","招商银行","邮储银行","交通银行","浦发银行","民生银行","兴业银行","平安银行","中信银行","华夏银行","广发银行","光大银行","北京银行","宁波银行"],bankname:"",info:{},textset:{}}},onLoad:function(n){this.opt=e.getopts(n),this.isload=!0,this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var n=this;n.loading=!0,e.get("ApiAdminFinance/mdtxset",{},(function(t){n.loading=!1,n.info=t.info,n.loaded()}))},formSubmit:function(n){var t=n.detail.value,a=t.bankname,i=t.bankcarduser,o=t.bankcardnum,u=t.weixin,c=t.aliaccount,d=t.aliaccountname,r=t.code||"";e.showLoading("提交中"),e.post("ApiAdminFinance/mdtxset",{bankname:a,bankcarduser:i,bankcardnum:o,weixin:u,aliaccount:c,aliaccountname:d,code:r},(function(n){e.showLoading(!1),1==n.status?(e.success(n.msg),setTimeout((function(){e.goback(!0)}),1e3)):e.error(n.msg)}))},bindBanknameChange:function(n){this.bankname=this.banklist[n.detail.value]}}};t.default=i},df5a:function(n,t,a){}},[["0738","common/runtime","common/vendor"]]]);