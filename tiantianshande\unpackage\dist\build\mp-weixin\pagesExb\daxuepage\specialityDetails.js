require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExb/daxuepage/specialityDetails"],{"2f93":function(t,n,e){"use strict";e.r(n);var a=e("bff9"),i=e("35ca");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);var u=e("828b"),d=Object(u["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=d.exports},"35ca":function(t,n,e){"use strict";e.r(n);var a=e("f5695"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=i.a},7964:function(t,n,e){"use strict";(function(t,n){var a=e("47a9");e("06e9");a(e("3240"));var i=a(e("2f93"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},bff9:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return a}));var a={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},i=function(){var t=this.$createElement;this._self._c},o=[]},f5695:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,data:{},id:""}},onLoad:function(t){this.opt=a.getopts(t),this.id=this.opt.id?this.opt.id:"",this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,a.get("ApiDaxue/getDetailById",{id:t.id},(function(n){t.loading=!1,t.data=n.data,t.loaded()}))}}};n.default=i}},[["7964","common/runtime","common/vendor"]]]);