require('../../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExb/filem/components/pdf-viewer"],{"12b2":function(e,r,t){"use strict";t.d(r,"b",(function(){return n})),t.d(r,"c",(function(){return o})),t.d(r,"a",(function(){}));var n=function(){var e=this.$createElement;this._self._c},o=[]},"33fe":function(e,r,t){"use strict";t.r(r);var n=t("67d5"),o=t.n(n);for(var a in n)["default"].indexOf(a)<0&&function(e){t.d(r,e,(function(){return n[e]}))}(a);r["default"]=o.a},"67d5":function(e,r,t){"use strict";(function(e){var n;Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;try{n=t("f355")}catch(a){console.error("PDF.js导入失败:",a),n=null}var o={name:"PdfViewer",props:{url:{type:String,required:!0}},data:function(){return{loading:!0,error:null,pdfDoc:null,currentPage:1,totalPages:0,canvasWidth:0,canvasHeight:0,scale:1,canvasContext:null,useIframe:!1}},mounted:function(){var r=this;if(console.log("PDF预览组件加载，URL:",this.url),!n)return this.loading=!1,this.error="未能加载PDF.js库，无法渲染PDF",void console.error("PDF.js库不可用");try{n.GlobalWorkerOptions&&(n.GlobalWorkerOptions.workerSrc="../pdfjs/pdf.worker.min.js");var t=e.createSelectorQuery().in(this);t.select(".pdf-canvas").fields({node:!0,size:!0}).exec((function(t){if(!t||!t[0])return r.error="Canvas元素未找到",void(r.loading=!1);try{var n=e.createCanvasContext("pdf-canvas",r);r.canvasContext=n}catch(a){return console.error("创建Canvas上下文失败:",a),r.error="无法初始化PDF渲染环境",void(r.loading=!1)}r.loadPdf()}))}catch(a){console.error("PDF预览组件初始化失败:",a),this.loading=!1,this.error="PDF预览初始化失败: ".concat(a.message)}},methods:{loadPdf:function(){var e=this;this.loading=!0,this.error=null,console.log("开始加载PDF文件:",this.url);try{var r={url:this.url,withCredentials:!0},t=n.getDocument(r);if(!t||!t.promise)throw new Error("PDF加载任务创建失败");t.promise.then((function(r){console.log("PDF加载成功，总页数:",r.numPages),e.pdfDoc=r,e.totalPages=r.numPages,e.loading=!1,e.renderPage(e.currentPage)})).catch((function(r){e.loading=!1,e.error="无法加载PDF文件: ".concat(r.message),console.error("PDF加载错误:",r)}))}catch(a){console.error("加载PDF文件失败:",a),this.loading=!1,this.error="PDF加载失败: ".concat(a.message)}},renderPage:function(e){var r=this;if(!this.useIframe){this.loading=!0;try{this.pdfDoc.getPage(e).then((function(t){var n=t.getViewport({scale:r.scale});r.canvasWidth=n.width,r.canvasHeight=n.height;var o={canvasContext:r.canvasContext,viewport:n};t.render(o).promise.then((function(){r.loading=!1,r.currentPage=e,r.canvasContext&&r.canvasContext.draw&&r.canvasContext.draw()})).catch((function(e){r.loading=!1,r.error="无法渲染PDF页面: ".concat(e.message),console.error("PDF渲染错误:",e)}))})).catch((function(e){r.loading=!1,r.error="无法获取PDF页面: ".concat(e.message),console.error("获取PDF页面错误:",e)}))}catch(a){this.loading=!1,this.error="渲染PDF页面失败: ".concat(a.message),console.error("渲染PDF页面异常:",a)}}},prevPage:function(){this.useIframe||this.currentPage>1&&this.renderPage(this.currentPage-1)},nextPage:function(){this.useIframe||this.currentPage<this.totalPages&&this.renderPage(this.currentPage+1)}}};r.default=o}).call(this,t("df3c")["default"])},9790:function(e,r,t){},ec1f:function(e,r,t){"use strict";var n=t("9790"),o=t.n(n);o.a},fd108:function(e,r,t){"use strict";t.r(r);var n=t("12b2"),o=t("33fe");for(var a in o)["default"].indexOf(a)<0&&function(e){t.d(r,e,(function(){return o[e]}))}(a);t("ec1f");var c=t("828b"),s=Object(c["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);r["default"]=s.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesExb/filem/components/pdf-viewer-create-component',
    {
        'pagesExb/filem/components/pdf-viewer-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("fd108"))
        })
    },
    [['pagesExb/filem/components/pdf-viewer-create-component']]
]);
