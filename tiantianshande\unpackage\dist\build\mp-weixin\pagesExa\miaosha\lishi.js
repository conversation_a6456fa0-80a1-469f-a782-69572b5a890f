require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/miaosha/lishi"],{"115d":function(t,n,a){"use strict";a.d(n,"b",(function(){return e})),a.d(n,"c",(function(){return i})),a.d(n,"a",(function(){return o}));var o={nomore:function(){return a.e("components/nomore/nomore").then(a.bind(null,"3892"))},nodata:function(){return a.e("components/nodata/nodata").then(a.bind(null,"101c"))},loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},e=function(){var t=this,n=t.$createElement,a=(t._self._c,t.isload?t.__map(t.datalist,(function(n,a){var o=t.__get_orig(n),e=t.formatTime(n.clear_time),i=n.express_no?t.formatTime(n.express_time):null;return{$orig:o,m0:e,m1:i}})):null);t.$mp.data=Object.assign({},{$root:{l0:a}})},i=[]},"1c6f":function(t,n,a){"use strict";(function(t,n){var o=a("47a9");a("06e9");o(a("3240"));var e=o(a("3367"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(e.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},3367:function(t,n,a){"use strict";a.r(n);var o=a("115d"),e=a("a691");for(var i in e)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(i);a("d879");var u=a("828b"),r=Object(u["a"])(e["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=r.exports},"849b":function(t,n,a){},a691:function(t,n,a){"use strict";a.r(n);var o=a("ed62"),e=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return o[t]}))}(i);n["default"]=e.a},d879:function(t,n,a){"use strict";var o=a("849b"),e=a.n(o);e.a},ed62:function(t,n,a){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,datalist:[],pagenum:1,nomore:!1,nodata:!1,totalCount:0,totalAmount:0}},onLoad:function(t){this.opt=a.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},methods:{getdata:function(t){var n=this;t||(this.pagenum=1,this.datalist=[]),n.nodata=!1,n.nomore=!1,n.loading=!0,a.post("ApiMiaosha/historyList",{pagenum:n.pagenum},(function(t){if(n.loading=!1,t&&t.datalist){var a=t.datalist;n.totalCount=t.total_count||0,n.totalAmount=t.total_amount||0,1==n.pagenum?(n.datalist=a,0==a.length&&(n.nodata=!0),n.isload=!0):0==a.length?n.nomore=!0:n.datalist=n.datalist.concat(a)}else n.nodata=!0}))},formatTime:function(t){var n=new Date(1e3*t);return n.getFullYear()+"-"+(n.getMonth()+1).toString().padStart(2,"0")+"-"+n.getDate().toString().padStart(2,"0")+" "+n.getHours().toString().padStart(2,"0")+":"+n.getMinutes().toString().padStart(2,"0")},copyExpress:function(n){t.setClipboardData({data:n,success:function(){t.showToast({title:"物流单号已复制",icon:"none"})}})}}};n.default=o}).call(this,a("df3c")["default"])}},[["1c6f","common/runtime","common/vendor"]]]);