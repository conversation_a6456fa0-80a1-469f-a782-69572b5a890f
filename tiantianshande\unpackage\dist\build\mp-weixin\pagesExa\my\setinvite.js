require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/my/setinvite"],{"47f0":function(e,t,n){"use strict";var i=n("652b"),o=n.n(i);o.a},"652b":function(e,t,n){},"685c":function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("06e9");i(n("3240"));var o=i(n("e12c"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},7778:function(e,t,n){"use strict";n.r(t);var i=n("eea7"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);t["default"]=o.a},cf54:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return i}));var i={loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},o=function(){var e=this.$createElement,t=(this._self._c,this.isload?this.t("color1"):null),n=this.isload?this.t("color1rgb"):null;this.$mp.data=Object.assign({},{$root:{m0:t,m1:n}})},a=[]},e12c:function(e,t,n){"use strict";n.r(t);var i=n("cf54"),o=n("7778");for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);n("47f0");var r=n("828b"),u=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=u.exports},eea7:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,invite_code:"",inviteCodeType:0,inviteCodePlaceholder:"请输入邀请人手机号",parentInfo:{}}},onLoad:function(e){this.opt=i.getopts(e),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var e=this;this.loading=!0,i.get("ApiMy/setinvite",{},(function(t){e.loading=!1,e.inviteCodeType=t.invite_code_type||0,e.invite_code=t.current_invite_code||"",e.parentInfo=t.parent_info||{},0==e.inviteCodeType?e.inviteCodePlaceholder="请输入邀请人手机号":1==e.inviteCodeType?e.inviteCodePlaceholder="请输入邀请码":e.inviteCodePlaceholder="请输入邀请人ID",e.loaded()}))},formSubmit:function(e){var t=e.detail.value,n=t.invite_code;if(""==n){var o="请输入邀请码";return 0==this.inviteCodeType?o="请输入邀请人手机号":2==this.inviteCodeType&&(o="请输入邀请人ID"),void i.alert(o)}i.showLoading("提交中"),i.post("ApiMy/setinvitesub",{invite_code:n},(function(e){i.showLoading(!1),1==e.status?(i.success(e.msg),setTimeout((function(){i.goback(!0)}),1e3)):i.error(e.msg)}))},inviteCodeInput:function(e){this.invite_code=e.detail.value;var t=this;e.detail.value&&e.detail.value.length>=3?setTimeout((function(){t.queryParentInfo(e.detail.value)}),500):t.parentInfo={}},queryParentInfo:function(e){var t=this;i.post("ApiMy/queryparent",{invite_code:e},(function(e){1==e.status&&e.parent?t.parentInfo=e.parent:t.parentInfo={}}))}}};t.default=o}},[["685c","common/runtime","common/vendor"]]]);