require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/ranking-reward/index"],{1401:function(t,n,a){"use strict";var e=a("97c0"),o=a.n(e);o.a},"34bd1":function(t,n,a){"use strict";a.r(n);var e=a("e38a"),o=a("93e6");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return o[t]}))}(i);a("1401");var r=a("828b"),c=Object(r["a"])(o["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=c.exports},"93e6":function(t,n,a){"use strict";a.r(n);var e=a("dca1"),o=a.n(e);for(var i in e)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(i);n["default"]=o.a},"97c0":function(t,n,a){},b256:function(t,n,a){"use strict";(function(t,n){var e=a("47a9");a("06e9");e(a("3240"));var o=e(a("34bd1"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},dca1:function(t,n,a){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=getApp(),e={data:function(){return{rulesList:[],statsData:{issued_reward:"0.00",pending_reward:"0.00",total_reward:"0.00",rules_with_ranking:0,recent_records:[]},loading:!1}},onLoad:function(){this.initData()},onPullDownRefresh:function(){this.initData((function(){t.stopPullDownRefresh()}))},methods:{initData:function(t){this.getStatsData(),this.getRulesList(),t&&"function"===typeof t&&t()},getStatsData:function(){(new Date).getTime();console.log("".concat(this.formatTime(),"-INFO-[ranking-reward/index][getStatsData_001] 开始获取排名奖励统计数据")),this.loading=!0;var n=this;a.post("ApiPaimingjiang/getRankingStats",{},(function(a){n.loading=!1,1===a.status?(console.log("".concat(n.formatTime(),"-INFO-[ranking-reward/index][getStatsData_002] 获取排名奖励统计数据成功")),n.statsData=a.data):(console.log("".concat(n.formatTime(),"-ERROR-[ranking-reward/index][getStatsData_003] 获取排名奖励统计数据失败：").concat(a.msg)),t.showToast({title:a.msg||"获取统计数据失败",icon:"none"}))}))},getRulesList:function(){console.log("".concat(this.formatTime(),"-INFO-[ranking-reward/index][getRulesList_001] 开始获取排名奖励规则列表")),this.loading=!0;var n=this;a.post("ApiPaimingjiang/getRankingRules",{},(function(a){n.loading=!1,1===a.status?(console.log("".concat(n.formatTime(),"-INFO-[ranking-reward/index][getRulesList_002] 获取排名奖励规则列表成功，共").concat(a.data.length,"条数据")),n.rulesList=a.data):(console.log("".concat(n.formatTime(),"-ERROR-[ranking-reward/index][getRulesList_003] 获取排名奖励规则列表失败：").concat(a.msg)),t.showToast({title:a.msg||"获取规则列表失败",icon:"none"}))}))},goToRulePreview:function(n){console.log("".concat(this.formatTime(),"-INFO-[ranking-reward/index][goToRulePreview_001] 跳转到规则预览页，规则ID：").concat(n)),t.navigateTo({url:"/pagesExa/ranking-reward/preview?rule_id=".concat(n)})},goToRecords:function(){console.log("".concat(this.formatTime(),"-INFO-[ranking-reward/index][goToRecords_001] 跳转到奖励记录页")),t.navigateTo({url:"/pagesExa/ranking-reward/records"})},formatTime:function(){var t=new Date,n=t.getFullYear(),a=(t.getMonth()+1).toString().padStart(2,"0"),e=t.getDate().toString().padStart(2,"0"),o=t.getHours().toString().padStart(2,"0"),i=t.getMinutes().toString().padStart(2,"0"),r=t.getSeconds().toString().padStart(2,"0"),c=t.getMilliseconds().toString().padStart(3,"0");return"".concat(n,"-").concat(a,"-").concat(e," ").concat(o,":").concat(i,":").concat(r,",").concat(c)}}};n.default=e}).call(this,a("df3c")["default"])},e38a:function(t,n,a){"use strict";a.d(n,"b",(function(){return e})),a.d(n,"c",(function(){return o})),a.d(n,"a",(function(){}));var e=function(){var t=this.$createElement,n=(this._self._c,this.rulesList.length),a=this.statsData.recent_records&&0===this.statsData.recent_records.length;this.$mp.data=Object.assign({},{$root:{g0:n,g1:a}})},o=[]}},[["b256","common/runtime","common/vendor"]]]);