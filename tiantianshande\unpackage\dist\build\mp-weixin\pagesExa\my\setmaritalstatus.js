require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/my/setmaritalstatus"],{"0456":function(t,n,a){"use strict";a.d(n,"b",(function(){return s})),a.d(n,"c",(function(){return i})),a.d(n,"a",(function(){return e}));var e={loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},s=function(){var t=this.$createElement,n=(this._self._c,this.isload?this.t("color1"):null),a=this.isload?this.t("color1rgb"):null;this.$mp.data=Object.assign({},{$root:{m0:n,m1:a}})},i=[]},"2df0":function(t,n,a){"use strict";var e=a("681a"),s=a.n(e);s.a},"3b9a":function(t,n,a){"use strict";a.r(n);var e=a("0456"),s=a("4ce9");for(var i in s)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return s[t]}))}(i);a("2df0");var u=a("828b"),o=Object(u["a"])(s["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=o.exports},4508:function(t,n,a){"use strict";(function(t,n){var e=a("47a9");a("06e9");e(a("3240"));var s=e(a("3b9a"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(s.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"4ce9":function(t,n,a){"use strict";a.r(n);var e=a("4fc1"),s=a.n(e);for(var i in e)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(i);n["default"]=s.a},"4fc1":function(t,n,a){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),s={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,marital_status:"",statusIndex:0,statusList:["请选择","单身","恋爱中","已婚","离异","丧偶","保密"]}},onLoad:function(t){this.opt=e.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,e.get("ApiMy/set",{},(function(n){if(t.loading=!1,t.marital_status=n.userinfo.marital_status||"",t.marital_status){var a=t.statusList.findIndex((function(n){return n===t.marital_status}));t.statusIndex=a>0?a:0}t.loaded()}))},bindPickerChange:function(t){this.statusIndex=t.detail.value},saveStatus:function(){var t=this.statusList[this.statusIndex];0!==this.statusIndex?(e.showLoading("提交中"),e.post("ApiMy/setfield",{marital_status:t},(function(t){e.showLoading(!1),1==t.status?(e.success(t.msg),setTimeout((function(){e.goback(!0)}),1e3)):e.error(t.msg)}))):e.alert("请选择情感状态")}}};n.default=s},"681a":function(t,n,a){}},[["4508","common/runtime","common/vendor"]]]);