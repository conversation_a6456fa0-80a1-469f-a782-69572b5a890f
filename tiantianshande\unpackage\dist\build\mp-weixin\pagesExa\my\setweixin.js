require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/my/setweixin"],{"4aa0":function(n,t,e){"use strict";(function(n,t){var i=e("47a9");e("06e9");i(e("3240"));var o=i(e("739e"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"5ab2":function(n,t,e){"use strict";var i=e("969f"),o=e.n(i);o.a},"62b9":function(n,t,e){"use strict";e.r(t);var i=e("6524"),o=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(a);t["default"]=o.a},6524:function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,weixin:""}},onLoad:function(n){this.opt=i.getopts(n),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var n=this;n.loading=!0,i.get("ApiMy/set",{},(function(t){n.loading=!1,n.weixin=t.userinfo.weixin||"",n.loaded()}))},formSubmit:function(n){var t=n.detail.value,e=t.weixin;""!=e?(i.showLoading("提交中"),i.post("ApiMy/setfield",{weixin:e},(function(n){i.showLoading(!1),1==n.status?(i.success(n.msg),setTimeout((function(){i.goback(!0)}),1e3)):i.error(n.msg)}))):i.alert("请输入微信号")}}};t.default=o},"739e":function(n,t,e){"use strict";e.r(t);var i=e("df21f"),o=e("62b9");for(var a in o)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(a);e("5ab2");var u=e("828b"),r=Object(u["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=r.exports},"969f":function(n,t,e){},df21f:function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){return i}));var i={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},o=function(){var n=this.$createElement,t=(this._self._c,this.isload?this.t("color1"):null),e=this.isload?this.t("color1rgb"):null;this.$mp.data=Object.assign({},{$root:{m0:t,m1:e}})},a=[]}},[["4aa0","common/runtime","common/vendor"]]]);