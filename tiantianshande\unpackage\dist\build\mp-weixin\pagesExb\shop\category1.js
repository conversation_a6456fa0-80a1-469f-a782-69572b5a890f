require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExb/shop/category1"],{"21aa":function(t,n,e){"use strict";(function(t,n){var a=e("47a9");e("06e9");a(e("3240"));var o=a(e("af2c"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"262e":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,data:[],bid:0}},onLoad:function(t){this.opt=a.getopts(t),this.bid=this.opt.bid?this.opt.bid:0,this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,a.get("ApiShop/category1",{bid:t.bid},(function(n){t.loading=!1,t.data=n.data,t.loaded()}))}}};n.default=o},"5cd1":function(t,n,e){"use strict";var a=e("7c363"),o=e.n(a);o.a},"7c363":function(t,n,e){},8679:function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){return a}));var a={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},o=function(){var t=this.$createElement;this._self._c},i=[]},af2c:function(t,n,e){"use strict";e.r(n);var a=e("8679"),o=e("b6dc");for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(i);e("5cd1");var u=e("828b"),c=Object(u["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=c.exports},b6dc:function(t,n,e){"use strict";e.r(n);var a=e("262e"),o=e.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(i);n["default"]=o.a}},[["21aa","common/runtime","common/vendor"]]]);