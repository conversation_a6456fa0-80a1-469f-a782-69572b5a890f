require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/my/mydata"],{"0ad9":function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return u})),e.d(t,"a",(function(){return a}));var a={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},o=function(){var n=this.$createElement;this._self._c},u=[]},"125f":function(n,t,e){"use strict";(function(n,t){var a=e("47a9");e("06e9");a(e("3240"));var o=a(e("12db"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"12db":function(n,t,e){"use strict";e.r(t);var a=e("0ad9"),o=e("85a9");for(var u in o)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(u);e("c0ff");var r=e("828b"),i=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=i.exports},5992:function(n,t,e){},"6bda":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,userinfo:{},levearr:{}}},onLoad:function(n){this.opt=a.getopts(n),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var n=this;n.loading=!0,a.get("ApiMy/mydata",{},(function(t){n.loading=!1,n.userinfo=t.userinfo,n.levearr=t.levearr,n.loaded()}))}}};t.default=o},"85a9":function(n,t,e){"use strict";e.r(t);var a=e("6bda"),o=e.n(a);for(var u in a)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(u);t["default"]=o.a},c0ff:function(n,t,e){"use strict";var a=e("5992"),o=e.n(a);o.a}},[["125f","common/runtime","common/vendor"]]]);